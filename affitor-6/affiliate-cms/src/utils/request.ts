/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
export class API {
  private readonly client: AxiosInstance;

  constructor(config: AxiosRequestConfig) {
    this.client = axios.create(config);
    this.client.interceptors.request.use(
      function (config: InternalAxiosRequestConfig) {
        config.timeout = 30000;
        return config;
      },
      function (error: any) {
        console.log('❌ request interceptors', error);
        return Promise.reject({ message: error.message });
      }
    );
    console.log(process.env.RAPIDAPI_KEY);
  }

  post(path: string, data: any, config?: AxiosRequestConfig): Promise<AxiosResponse['data']> {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await this.client.post(path, data, config);
        return resolve(res.data);
      } catch (error: any) {
        return reject({
          message: error.message,
          status: error.response?.status,
          data: error.response?.data,
          statusText: error.response?.statusText,
        });
      }
    });
  }

  get(path: string, config: AxiosRequestConfig) {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await this.client.get(path, config);
        return resolve(res.data);
      } catch (error: any) {
        return reject({
          message: error.message,
          status: error.response?.status,
          data: error.response?.data,
          statusText: error.response?.statusText,
        });
      }
    });
  }
}

export const YoutubeClient = {
  client: new API({
    baseURL: process.env.YOUTUBE_SEARCH_URL || 'https://youtube-api49.p.rapidapi.com',
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-host': process.env.YOUTUBE_API_HOST || 'youtube-api49.p.rapidapi.com',
      'x-rapidapi-key': process.env.RAPIDAPI_KEY,
    },
  }),

  search: async (keyword: string) => {
    const response: any = await YoutubeClient.client.get('api/search', {
      params: {
        q: keyword,
        maxResults: 50,
        regionCode: 'US',
      },
    });
    return response;
  },

  getVideoInfo: async (videoId: string) => {
    const response: any = await YoutubeClient.client.get('api/video/info', {
      params: {
        videoId,
      },
    });
    return response;
  },
};

export const TiktokClient = {
  client: new API({
    baseURL: process.env.TIKTOK_SEARCH_URL || 'https://tiktok-api23.p.rapidapi.com',
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-host': process.env.TIKTOK_API_HOST || 'tiktok-api23.p.rapidapi.com',
      'x-rapidapi-key': process.env.RAPIDAPI_KEY,
    },
  }),

  search: async (keyword: string) => {
    console.log('Search Tiktok');
    const response: any = await TiktokClient.client.get('api/search/video', {
      params: {
        keyword,
        cursor: 0,
        search_id: 0,
      },
    });
    return response;
  },

  getVideoDetail: async (videoId: string) => {
    try {
      const response: any = await TiktokClient.client.get('api/post/detail', {
        params: {
          videoId,
        },
      });

      // Extract and return only the thumbnail information to keep response focused
      if (response?.itemInfo?.itemStruct?.video?.originCover) {
        return {
          thumbnail: response.itemInfo.itemStruct.video.originCover,
          success: true,
        };
      }

      return { success: false };
    } catch (error) {
      console.error(`Error fetching detail for TikTok video ${videoId}:`, error);
      return { success: false, error };
    }
  },
};

export const TiktokCreativeCenterClient = {
  client: new API({
    baseURL: 'https://tiktok-creative-center-api.p.rapidapi.com',
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-host': 'tiktok-creative-center-api.p.rapidapi.com',
      'x-rapidapi-key': process.env.RAPIDAPI_KEY,
    },
  }),

  searchAds: async (params: {
    page?: number;
    limit?: number;
    period?: number;
    country?: string;
    order_by?: string;
    keyword?: string;
  }) => {
    console.log('Searching TikTok Ads');
    const defaultParams = {
      page: 1,
      limit: 20,
      period: 180,
      country: 'US',
      order_by: 'ctr',
    };

    const mergedParams = { ...defaultParams, ...params };

    try {
      const response: any = await TiktokCreativeCenterClient.client.get('/api/trending/ads', {
        params: mergedParams,
      });
      return response;
    } catch (error) {
      console.error('Error searching TikTok ads:', error);
      throw error;
    }
  },

  getAdDetail: async (adId: string) => {
    console.log(`Getting TikTok Ad detail for ${adId}`);
    try {
      const response: any = await TiktokCreativeCenterClient.client.get(
        '/api/trending/ads/detail',
        {
          params: {
            ads_id: adId,
          },
        }
      );
      return response;
    } catch (error) {
      console.error(`Error fetching ad detail for ${adId}:`, error);
      throw error;
    }
  },
};

export const TiktokDownloadClient = {
  client: new API({
    baseURL:
      process.env.TIKTOK_DOWNLOAD_URL || 'https://tiktok-download-without-watermark.p.rapidapi.com',
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-host':
        process.env.TIKTOK_DOWNLOAD_API_HOST || 'tiktok-download-without-watermark.p.rapidapi.com',
      'x-rapidapi-key': process.env.RAPIDAPI_KEY,
    },
  }),

  getDownloadLink: async (videoUrl: string, hd: number = 0) => {
    console.log('Get TikTok Download Link');
    try {
      const response: any = await TiktokDownloadClient.client.get('analysis', {
        params: {
          url: videoUrl,
          hd,
        },
      });
      return response;
    } catch (error: any) {
      console.error('Error fetching TikTok download link:', error);
      throw error;
    }
  },
};

export const XClient = {
  client: new API({
    baseURL: process.env.X_SEARCH_URL,
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-host': process.env.X_API_HOST,
      'x-rapidapi-key': process.env.RAPIDAPI_KEY,
    },
  }),

  search: async (keyword: string) => {
    console.log('Search X');
    const response: any = await XClient.client.get('', {
      params: {
        query: keyword,
        search_type: 'Top',
      },
    });
    return response;
  },
};

export const RedditClient = {
  client: new API({
    baseURL: process.env.REDDIT_SEARCH_URL,
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-host': process.env.REDDIT_API_HOST,
      'x-rapidapi-key': process.env.RAPIDAPI_KEY,
    },
  }),

  search: async (keyword: string) => {
    console.log('Search Reddit');
    const response: any = await RedditClient.client.get('search_posts_v3', {
      params: {
        query: keyword,
        sort: 'RELEVANCE',
        time: 'all',
        nsfw: 0,
      },
    });
    return response;
  },
};

export const SimilarWebClient = {
  client: new API({
    baseURL: process.env.SIMILAR_WEB_URL,
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-host': process.env.SIMILAR_WEB_API_HOST,
      'x-rapidapi-key': process.env.SIMILAR_WEB_RAPIDAPI_KEY,
    },
  }),

  getTraffic: async (affiliateUrl: string) => {
    console.log('getTraffic SimilarWeb');
    const response: any = await SimilarWebClient.client.get('traffic', {
      params: {
        domain: affiliateUrl,
      },
    });
    // return similarWebResponse;
    return response;
  },
};

export const TiktokTranscriptClient = {
  client: new API({
    baseURL:
      process.env.TIKTOK_TRANSCRIPT_URL || 'https://tiktok-subtitles-and-translates.p.rapidapi.com',
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-host':
        process.env.TIKTOK_TRANSCRIPT_API_HOST || 'tiktok-subtitles-and-translates.p.rapidapi.com',
      'x-rapidapi-key': process.env.RAPIDAPI_KEY,
    },
  }),

  getTranscript: async (tiktokUrl: string) => {
    console.log('Get TikTok Transcript');
    try {
      const response: any = await TiktokTranscriptClient.client.get('subtitles', {
        params: {
          url: tiktokUrl,
          type: 'text',
        },
      });

      // Parse the response to match the required structure
      return {
        transcript: response.success ? response.message : '',
      };
    } catch (error: any) {
      console.error('Error fetching TikTok transcript:', error);
      return {
        transcript: '',
      };
    }
  },
};

export const YoutubeTranscriptClient = {
  client: new API({
    baseURL:
      process.env.YOUTUBE_TRANSCRIPT_URL ||
      'https://youtube-transcript-subtitles-captions.p.rapidapi.com',
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-host':
        process.env.YOUTUBE_TRANSCRIPT_API_HOST ||
        'youtube-transcript-subtitles-captions.p.rapidapi.com',
      'x-rapidapi-key': process.env.RAPIDAPI_KEY,
    },
  }),

  // Existing API-based method
  getTranscriptFromAPI: async (videoId: string) => {
    console.log('Get YouTube Transcript from API');
    try {
      const response: any = await YoutubeTranscriptClient.client.get('/any', {
        params: {
          videoId,
        },
      });

      console.log('YouTube Transcript API Response:', !!response.body);
      return {
        transcript: response.body || '',
        source: 'api',
      };
    } catch (error: any) {
      console.error('Error fetching YouTube transcript from API:', error);
      return {
        transcript: '',
        source: 'api',
        error: error.message,
      };
    }
  },

  // Updated method using youtubei.js (Innertube) library
  getTranscriptFromLibrary: async (videoId: string, isOriginal = false) => {
    console.log('Get YouTube Transcript using Innertube library');
    try {
      // Dynamically import Innertube to avoid ESM/CommonJS compatibility issues
      const { Innertube } = await import('youtubei.js');

      // Initialize the YouTube client
      const youtube = await Innertube.create({
        lang: 'en',
        location: 'US',
        retrieve_player: false,
      });

      // Get video info and transcript
      const info = await youtube.getInfo(videoId);
      const transcriptData = await info.getTranscript();

      // Extract and format the transcript segments
      const transcriptSegments = transcriptData?.transcript?.content?.body?.initial_segments || [];

      if (isOriginal) {
        // Get total video duration from the last segment's end time
        const lastSegment = transcriptSegments[transcriptSegments.length - 1];
        const totalDurationMs = lastSegment ? parseInt(lastSegment.end_ms) || 0 : 0;
        const totalDurationSec = totalDurationMs / 1000;

        // Calculate compression ratio for timestamp display
        const targetDurationSec = totalDurationSec < 30 ? totalDurationSec / 2 : 30;

        // Filter valid segments
        const validSegments = transcriptSegments.filter(
          (segment) => segment.snippet?.text && segment.snippet.text !== 'undefined'
        );

        if (validSegments.length === 0) {
          return { transcript: '', source: 'library' };
        }

        // Sort segments by start time (to ensure proper ordering)
        validSegments.sort((a, b) => parseInt(a.start_ms) - parseInt(b.start_ms));

        // Implement the chunking algorithm as described
        const chunks = [];
        const remainingSegments = [...validSegments];
        let numberOfChunk = 1;

        while (remainingSegments.length > 0) {
          const targetMs = 30000 * numberOfChunk;
          const currentChunk = [];

          // Find all segments with end_ms less than targetMs
          let i = 0;
          while (i < remainingSegments.length) {
            if (parseInt(remainingSegments[i].end_ms) <= targetMs) {
              currentChunk.push(remainingSegments[i]);
              remainingSegments.splice(i, 1); // Remove this segment from remainingSegments
            } else {
              i++; // Only increment if we didn't remove a segment
            }
          }

          if (currentChunk.length > 0) {
            chunks.push(currentChunk);
          }

          numberOfChunk++;
        }

        // Format each chunk with compressed timestamps
        const formattedTranscript = chunks
          .map((chunk) => {
            // Get the timestamp from the first segment in the chunk
            const startMs = parseInt(chunk[0].start_ms);
            // Apply compression ratio to timestamp for display
            const compressedTimeMs = startMs;
            const timeText = YoutubeTranscriptClient.formatTime(compressedTimeMs);

            // Concatenate all text in the chunk
            const text = chunk.map((segment) => segment.snippet.text).join(' ');

            return `${timeText}: ${text}`;
          })
          .join('\n\n');

        return {
          transcript: formattedTranscript || '',
          source: 'library',
        };
      } else {
        // Original behavior - join all segments without timestamps
        const formattedTranscript = transcriptSegments
          .map((segment) => segment.snippet.text)
          .join(' ');

        return {
          transcript: formattedTranscript || '',
          source: 'library',
        };
      }
    } catch (error: any) {
      console.error('Error fetching YouTube transcript using Innertube library:', error);
      return {
        transcript: '',
        source: 'library',
        error: error.message,
      };
    }
  },

  // Helper function to format time from milliseconds to MM:SS format
  formatTime: (ms: number): string => {
    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    // If time exceeds 59:59, format it to include hours
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Standard MM:SS format
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  },

  // Combined method that tries library first, then falls back to API
  getTranscript: async (videoId: string, isOriginal = false) => {
    console.log('Get YouTube Transcript (combined method)');
    try {
      // Try the library first
      const libraryResult = await YoutubeTranscriptClient.getTranscriptFromLibrary(
        videoId,
        isOriginal
      );

      // If library method succeeds with content, return it
      if (libraryResult.transcript) {
        return {
          transcript: libraryResult.transcript,
          source: libraryResult.source,
        };
      }

      // Otherwise fall back to API
      const apiResult = await YoutubeTranscriptClient.getTranscriptFromAPI(videoId);
      return {
        transcript: apiResult.transcript,
        source: apiResult.source,
      };
    } catch (error: any) {
      console.error('Error in combined YouTube transcript fetch:', error);
      // Last resort fallback to API
      return YoutubeTranscriptClient.getTranscriptFromAPI(videoId);
    }
  },
};

export const N8nClient = {
  client: new API({
    baseURL: process.env.N8N_BASE_URL || 'https://tovietthang.app.n8n.cloud',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': process.env.N8N_API_KEY,
      Authorization: process.env.N8N_AUTH || 'Basic YWRtaW46YWRtaW4=',
    },
  }),

  sendToChatGPT: async (message: string, sessionId: string) => {
    console.log('Send to N8N ChatGPT');
    const response: any = await N8nClient.client.post('/webhook/chatgpt', {
      chatInput: message,
      sessionId: sessionId,
    });

    // Parse the response which is an array with an object containing 'output' field
    if (Array.isArray(response) && response.length > 0 && response[0].output) {
      return response[0].output;
    }
    return response;
  },

  sendToGemini: async (message: string, sessionId: string) => {
    console.log('Send to N8N Gemini: ', { sessionId });

    const response: any = await N8nClient.client.post('/webhook/gemini', {
      chatInput: message,
      sessionId: sessionId,
    });

    // Parse the response which is an array with an object containing 'output' field
    if (Array.isArray(response) && response.length > 0 && response[0].output) {
      return response[0].output;
    }
    return response;
  },
};
