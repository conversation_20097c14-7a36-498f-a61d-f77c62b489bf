import { errors } from '@strapi/utils';
import { normalizeUser } from '../../utils/normalize';

const { ApplicationError, ValidationError } = errors;

export default (plugin) => {
  // Utility function removed and imported from utils

  //   // Add a new controller function
  //   plugin.controllers.user.getCustomUsers = async (ctx) => {
  //     // Example: fetch all users with custom logic
  //     const users = await strapi.plugin('users-permissions').service('user').fetchAll(ctx.query); // Use your own query/filter logic here
  //     ctx.body = users;
  //   };

  plugin.controllers.user.me = async (ctx) => {
    if (!ctx.state.user) {
      return ctx.unauthorized('You are not logged in');
    }

    // init user-tracking-request if not exist
    await strapi
      .service('api::user-tracking-request.user-tracking-request')
      .getUserTracking(ctx.state.user.id);

    const user = await strapi.entityService.findOne(
      'plugin::users-permissions.user',
      ctx.state.user.id,
      {
        populate: {
          user_tracking_request: {
            populate: ['subscription_tier', 'transaction'],
          },
          referrer: true,
        },
      }
    );

    ctx.body = normalizeUser(user);
  };

  plugin.controllers.user.update = async (ctx) => {
    const { user } = ctx.state;

    if (!user) {
      throw new ApplicationError('You must be logged in to update your profile');
    }

    const {
      first_name,
      last_name,
      address,
      apt,
      city,
      country,
      zip_code,
      state,
      paypal_email,
      bank_transfer,
      referrer_code,
    } = ctx.request.body;

    console.log('Updating profile for user:', ctx.request.body);

    // Validate input data
    const validationErrors = [];

    // Profile validation
    if (first_name !== undefined && (typeof first_name !== 'string' || first_name.trim() === '')) {
      validationErrors.push('First name must be a non-empty string');
    }

    if (last_name !== undefined && (typeof last_name !== 'string' || last_name.trim() === '')) {
      validationErrors.push('Last name must be a non-empty string');
    }

    // Address validation
    if (address !== undefined && typeof address !== 'string') {
      validationErrors.push('Address must be a string');
    }

    if (apt !== undefined && typeof apt !== 'string') {
      validationErrors.push('Apartment/suite must be a string');
    }

    if (city !== undefined && typeof city !== 'string') {
      validationErrors.push('City must be a string');
    }

    if (country !== undefined && typeof country !== 'string') {
      validationErrors.push('Country must be a string');
    }

    if (zip_code !== undefined && typeof zip_code !== 'string') {
      validationErrors.push('Zip code must be a string');
    }

    if (state !== undefined && typeof state !== 'string') {
      validationErrors.push('State must be a string');
    }

    // Paypal email validation
    if (paypal_email !== undefined) {
      if (typeof paypal_email !== 'string') {
        validationErrors.push('PayPal email must be a string');
      } else {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(paypal_email)) {
          validationErrors.push('Invalid PayPal email format');
        }
      }
    }

    // Bank transfer validation
    if (bank_transfer !== undefined) {
      if (typeof bank_transfer !== 'object' || bank_transfer === null) {
        validationErrors.push('Bank transfer details must be an object');
      } else {
        // Required fields
        if (
          bank_transfer.account_number !== undefined &&
          (typeof bank_transfer.account_number !== 'string' ||
            bank_transfer.account_number.trim() === '')
        ) {
          validationErrors.push('Account number must be a non-empty string');
        }

        if (
          bank_transfer.swift_code !== undefined &&
          (typeof bank_transfer.swift_code !== 'string' || bank_transfer.swift_code.trim() === '')
        ) {
          validationErrors.push('Swift code must be a non-empty string');
        }

        // Optional fields
        if (
          bank_transfer.first_name !== undefined &&
          typeof bank_transfer.first_name !== 'string'
        ) {
          validationErrors.push('Bank account first name must be a string');
        }

        if (bank_transfer.last_name !== undefined && typeof bank_transfer.last_name !== 'string') {
          validationErrors.push('Bank account last name must be a string');
        }

        if (
          bank_transfer.business_name !== undefined &&
          typeof bank_transfer.business_name !== 'string'
        ) {
          validationErrors.push('Business name must be a string');
        }

        if (bank_transfer.country !== undefined && typeof bank_transfer.country !== 'string') {
          validationErrors.push('Bank country must be a string');
        }

        if (bank_transfer.city !== undefined && typeof bank_transfer.city !== 'string') {
          validationErrors.push('Bank city must be a string');
        }

        if (bank_transfer.state !== undefined && typeof bank_transfer.state !== 'string') {
          validationErrors.push('Bank state must be a string');
        }

        if (bank_transfer.address !== undefined && typeof bank_transfer.address !== 'string') {
          validationErrors.push('Bank address must be a string');
        }

        if (bank_transfer.zip_code !== undefined && typeof bank_transfer.zip_code !== 'string') {
          validationErrors.push('Bank zip code must be a string');
        }
      }
    }

    if (validationErrors.length > 0) {
      throw new ValidationError(validationErrors.join('. '));
    }

    try {
      // Fetch current user data to check for existing values
      const currentUser = await strapi.entityService.findOne(
        'plugin::users-permissions.user',
        user.id
      );

      // Create updateData object and only add fields that exist in request body
      const updateData: Record<string, any> = {};

      // Only add fields if they exist in the request
      if (first_name !== undefined) updateData.first_name = first_name;
      if (last_name !== undefined) updateData.last_name = last_name;
      if (address !== undefined) updateData.address = address;
      if (apt !== undefined) updateData.apt = apt;
      if (city !== undefined) updateData.city = city;
      if (country !== undefined) updateData.country = country;
      if (zip_code !== undefined) updateData.zip_code = zip_code;
      if (state !== undefined) updateData.state = state;
      if (paypal_email !== undefined) updateData.paypal_email = paypal_email;
      if (bank_transfer !== undefined) updateData.bank_transfer = bank_transfer;
      if (referrer_code !== undefined) {
        // find the referrer of the user
        const referrer = await strapi.entityService.findMany('api::referrer.referrer', {
          filters: {
            user: {
              id: user.id,
            },
          },
          limit: 1,
        });

        if (!referrer || referrer.length === 0) {
          throw new ApplicationError('No referrer found for this user');
        }

        // update the referrer code
        await strapi.entityService.update('api::referrer.referrer', referrer[0].id, {
          data: {
            referral_code: referrer_code,
          },
        });
      }

      // Update the user with only the fields that were provided
      const updatedUser = await strapi.entityService.update(
        'plugin::users-permissions.user',
        user.id,
        {
          data: updateData,
        }
      );

      ctx.body = normalizeUser(updatedUser);
    } catch (error) {
      throw new ApplicationError(`Error updating profile: ${error.message}`);
    }
  };

  return plugin;
};
