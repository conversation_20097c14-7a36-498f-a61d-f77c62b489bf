/**
 * subscription-tier router
 */

import { factories } from '@strapi/strapi';

export default {
  routes: [
    // Default routes
    {
      method: 'GET',
      path: '/subscription-tiers',
      handler: 'subscription-tier.find',
      config: {
        policies: [],
      },
    },
    {
      method: 'GET',
      path: '/subscription-tiers/:id',
      handler: 'subscription-tier.findOne',
      config: {
        policies: [],
      },
    },

    // Custom routes for subscription management
    {
      method: 'GET',
      path: '/subscription-tiers/pricing',
      handler: 'subscription-tier.getPricingTiers',
      config: {
        policies: [],
      },
    },
    {
      method: 'POST',
      path: '/subscription-tiers/create-checkout',
      handler: 'subscription-tier.createCheckoutSession',
      config: {
        // policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    {
      method: 'GET',
      path: '/subscription-tiers/confirm-checkout/:sessionId',
      handler: 'subscription-tier.confirmCheckoutSession',
      config: {
        // policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    {
      method: 'GET',
      path: '/subscription-tiers/user-subscription',
      handler: 'subscription-tier.getUserSubscription',
      config: {
        // policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    {
      method: 'POST',
      path: '/subscription-tiers/cancel',
      handler: 'subscription-tier.cancelSubscription',
      config: {
        // policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    {
      method: 'POST',
      path: '/subscription-tiers/update',
      handler: 'subscription-tier.updateSubscription',
      config: {
        // policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    // {
    //   method: 'POST',
    //   path: '/subscription-tiers/webhook',
    //   handler: 'subscription-tier.handleWebhook',
    //   config: {
    //     policies: [],
    //     auth: false,
    //   },
    // },
  ],
};
