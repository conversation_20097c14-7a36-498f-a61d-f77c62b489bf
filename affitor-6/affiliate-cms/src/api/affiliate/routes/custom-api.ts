export default {
  routes: [
    {
      method: 'GET',
      path: '/affiliates/:id/url',
      handler: 'affiliate.getAffiliateUrl',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/affiliates/:id/summary',
      handler: 'affiliate.getAffiliateSummary',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/affiliates/bulk-update-recurring-priority',
      handler: 'affiliate.bulkUpdateRecurringPriority',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
