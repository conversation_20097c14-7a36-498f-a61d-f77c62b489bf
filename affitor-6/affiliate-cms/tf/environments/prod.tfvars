# ECR Image configuration
ecr_image = "779037175265.dkr.ecr.us-east-1.amazonaws.com/affiliate:latest"
bucket_name    = "affiliate-ecs-service-app-configuration"

# AWS Region
aws_region = "us-east-1"

# VPC and Network settings
vpc_cidr = "10.0.0.0/16"
public_subnets = ["10.0.1.0/24", "10.0.2.0/24"]
private_subnets = ["10.0.3.0/24", "********/24"]

# ECS settings
ecs_cluster_name = "affiliate-prod-cluster"
ecs_service_name = "affiliate-prod-service"
container_port = 80
desired_count = 2
cpu = 256
memory = 512

# Load balancer settings
lb_name = "affiliate-prod-lb"
lb_internal = false
lb_listener_port = 80
lb_health_check_path = "/health"
engine_version = 16.6

# Tags
environment = "production"

# Add this to your tfvars file
certificate_arn = "arn:aws:acm:us-east-1:779037175265:certificate/039c4b1b-1743-4b3e-bf4b-e6bf28d45516"
ip_address = "***********"

# More aggressive cost savings (monitor performance closely)
serverless_min_capacity = 0.5  # Reduced minimum (50% savings at idle)
serverless_max_capacity = 3.0  # Reduced maximum (25% savings at peak)