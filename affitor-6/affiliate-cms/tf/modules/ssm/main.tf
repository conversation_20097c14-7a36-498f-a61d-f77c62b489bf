locals {
  # Use the current workspace as the environment name
  env              = terraform.workspace
  ssm_password_key = "/${local.env}/rds/master_password"
  rds_identifier   = "${local.env}-aurora-cluster"
}

resource "random_password" "rds_password" {
  length           = 16
  special          = true
  override_special = "!@#*()_-="
}

resource "aws_ssm_parameter" "rds_password" {
  name        = "/${var.environment}/rds/master_password"
  description = "Master password for RDS instance"
  type        = "SecureString"
  value       = var.database_password
}

resource "aws_ssm_parameter" "rds_username" {
  name        = "/${var.environment}/rds/master_username"
  description = "Master username for RDS instance"
  type        = "String"
  value       = var.database_username
}
