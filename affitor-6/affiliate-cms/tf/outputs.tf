output "vpc_id" {
  description = "The ID of the VPC"
  value       = module.vpc.vpc_id
}

output "public_subnet_ids" {
  description = "List of public subnet IDs"
  value       = module.vpc
}

output "private_subnet_ids" {
  description = "List of private subnet IDs"
  value       = module.vpc
}

output "ecs_cluster_name" {
  description = "Name of the ECS cluster"
  value       = module.ecs
}

output "ecs_service_name" {
  description = "Name of the ECS service"
  value       = module.ecs
}

output "load_balancer_dns" {
  description = "DNS name of the load balancer"
  value       = module.ecs
}
