/* Yoopta Editor Notion-style Customization */
.yoopta-selection-block {
  height: 5px;
}

/* Main editor container - clean, minimal like Notion */
.notion-editor-container {
  position: relative;
  width: 100% !important;
  max-width: 100% !important;
  min-height: 500px;
  background: transparent;
  cursor: text;
  box-sizing: border-box !important;
  display: block !important;
  flex: 1 1 100% !important;
}

.notion-content-area {
  width: 100% !important;
  max-width: 100% !important;
  min-height: 60vh;
  background: transparent;
  padding: 0;
  margin: 0;
  box-sizing: border-box !important;
}

/* Ensure editor container is clickable and focusable with full width */
.notion-editor-container {
  position: relative;
  width: 100% !important;
  max-width: 100% !important;
  min-height: 500px;
  background: transparent;
  cursor: text;
  outline: none;
  box-sizing: border-box !important;
}

.notion-editor-container:focus-within {
  outline: none;
}

/* Improve click-to-focus behavior */
.notion-editor-container:empty {
  min-height: 500px;
  cursor: text;
}

.notion-editor-container:empty::before {
  content: '';
  display: block;
  height: 100%;
  width: 100%;
  cursor: text;
}

/* Enhanced click-to-focus behavior */
.notion-editor-container {
  cursor: text;
  position: relative;
}

/* Make empty areas clickable */
.notion-editor-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

/* When editor has content, make the bottom area clickable */
.notion-editor-container:not(:empty)::after {
  pointer-events: auto;
  min-height: 100px;
  top: auto;
  bottom: 0;
  height: 100px;
  cursor: text;
}

/* Ensure blocks are clickable */
.notion-style-editor .yoopta-block {
  cursor: text;
  position: relative;
}

/* Visual feedback for hovering over blocks */
.notion-style-editor .yoopta-block:hover {
  background-color: rgba(55, 53, 47, 0.03);
  border-radius: 3px;
  transition: background-color 0.1s ease;
}

/* Focus state for blocks */
.notion-style-editor .yoopta-block:focus-within {
  background-color: rgba(55, 53, 47, 0.05);
  border-radius: 3px;
}

.notion-editor-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: rgba(55, 53, 47, 0.65);
  font-size: 14px;
  font-style: italic;
}

.notion-style-editor {
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  line-height: 1;
  color: rgb(55, 53, 47);
  min-height: 500px;
  position: relative;
  background: transparent;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  width: 100%;
}

/* Remove all default editor styling and ensure full width */
.notion-style-editor .yoopta-editor,
.notion-style-editor [data-yoopta-editor] {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  outline: none !important;
  min-height: 500px;
  cursor: text;
  width: 100% !important;
  max-width: 100% !important;
}

/* Enhanced contenteditable styling - exactly like Notion with full width */
.notion-style-editor [contenteditable="true"] {
  outline: none !important;
  cursor: text;
  caret-color: rgb(55, 53, 47);
  line-height: 1;
  font-size: 16px;
  color: rgb(55, 53, 47);
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  min-height: 1em;
  word-wrap: break-word;
  white-space: pre-wrap;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  position: relative;
}

.notion-style-editor [contenteditable="true"]:focus {
  outline: none !important;
  caret-color: rgb(55, 53, 47);
}

/* Ensure proper cursor behavior on Enter key */
.notion-style-editor [contenteditable="true"]:focus-within {
  caret-color: rgb(55, 53, 47) !important;
}

/* Dark theme support for contenteditable */
.dark .notion-style-editor [contenteditable="true"] {
  color: rgb(229, 231, 235) !important;
  caret-color: rgb(229, 231, 235) !important;
}

.dark .notion-style-editor [contenteditable="true"]:focus {
  caret-color: rgb(229, 231, 235) !important;
}

.dark .notion-style-editor [contenteditable="true"]:focus-within {
  caret-color: rgb(229, 231, 235) !important;
}

/* Improve click-to-focus precision */
.notion-style-editor [contenteditable="true"]:hover {
  background-color: rgba(55, 53, 47, 0.02);
  border-radius: 2px;
  transition: background-color 0.1s ease;
}

/* Visual indicator for focused content */
.notion-style-editor [contenteditable="true"]:focus {
  background-color: rgba(55, 53, 47, 0.03);
  border-radius: 2px;
}

/* Fix cursor positioning after line breaks */
.notion-style-editor br {
  line-height: 1;
}

.notion-style-editor div:empty::after {
  content: '';
  display: inline-block;
  width: 0;
}

/* Placeholder styling - subtle like Notion */
.notion-style-editor [data-yoopta-editor]:empty::before,
.notion-style-editor [contenteditable="true"]:empty::before {
  content: attr(data-placeholder);
  color: rgba(55, 53, 47, 0.4);
  pointer-events: none;
  position: absolute;
  font-size: 16px;
  line-height: 1;
}

/* Dark theme placeholder */
.dark .notion-style-editor [data-yoopta-editor]:empty::before,
.dark .notion-style-editor [contenteditable="true"]:empty::before {
  color: rgba(229, 231, 235, 0.4) !important;
}

/* Text selection - clean blue like Notion */
.notion-style-editor ::selection {
  background-color: rgba(46, 170, 220, 0.2);
  color: inherit;
}

/* Dark theme text selection */
.dark .notion-style-editor ::selection {
  background-color: rgba(46, 170, 220, 0.3);
  color: inherit;
}

/* Block styling - clean and minimal like Notion with full width */
.notion-style-editor .yoopta-block {
  margin: 1px 0 !important;
  padding: 3px 2px !important;
  border-radius: 3px;
  position: relative;
  transition: background-color 0.1s ease;
  line-height: 1;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.notion-style-editor .yoopta-block:hover {
  background-color: rgba(55, 53, 47, 0.06) !important;
}

.notion-style-editor .yoopta-block-selected {
  background-color: rgba(46, 170, 220, 0.1) !important;
}

.notion-style-editor .yoopta-block-focused {
  outline: none !important;
  background-color: transparent !important;
}

/* Block handles (drag indicators) */
.notion-style-editor .yoopta-block-handle {
  opacity: 0;
  transition: opacity 0.1s ease;
  cursor: grab;
  color: rgb(156, 163, 175);
}

.notion-style-editor .yoopta-block:hover .yoopta-block-handle {
  opacity: 1;
}

/* Typography - exact Notion spacing and sizing with full width */
.notion-style-editor h1 {
  font-size: 2.25rem !important;
  font-weight: 700 !important;
  line-height: 1.1 !important;
  margin: 2px 0 1px 0 !important;
  color: rgb(55, 53, 47) !important;
  padding: 3px 2px !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.notion-style-editor h2 {
  font-size: 1.875rem !important;
  font-weight: 600 !important;
  line-height: 1.2 !important;
  margin: 2px 0 1px 0 !important;
  color: rgb(55, 53, 47) !important;
  padding: 3px 2px !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.notion-style-editor h3 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  line-height: 1.2 !important;
  margin: 2px 0 1px 0 !important;
  color: rgb(55, 53, 47) !important;
  padding: 3px 2px !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.notion-style-editor p {
  font-size: 16px !important;
  line-height: 1.3 !important;
  color: rgb(55, 53, 47) !important;
  margin: 1px 0 !important;
  padding: 3px 2px !important;
  min-height: 1.3em !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Lists - Notion-style spacing with full width */
.notion-style-editor ul, .notion-style-editor ol {
  margin: 1px 0 !important;
  padding-left: 1.5rem !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.notion-style-editor li {
  margin: 1px 0 !important;
  padding: 3px 2px !important;
  line-height: 1.3 !important;
  color: rgb(55, 53, 47) !important;
  font-size: 16px !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Blockquotes - Notion style with full width */
.notion-style-editor blockquote {
  border-left: 3px solid rgb(55, 53, 47) !important;
  padding: 3px 2px 3px 16px !important;
  margin: 1px 0 !important;
  font-style: normal !important;
  color: rgb(55, 53, 47) !important;
  font-size: 16px !important;
  line-height: 1.3 !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Code blocks */
.notion-style-editor pre {
  background: rgb(247, 246, 243) !important;
  border: 1px solid rgb(227, 226, 224) !important;
  border-radius: 3px !important;
  padding: 1rem !important;
  margin: 0.5rem 0 !important;
  overflow-x: auto !important;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
}

.notion-style-editor code {
  background: rgba(135, 131, 120, 0.15) !important;
  color: #eb5757 !important;
  border-radius: 3px !important;
  padding: 0.2em 0.4em !important;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace !important;
  font-size: 85% !important;
}

/* Dividers */
.notion-style-editor hr {
  border: none !important;
  border-top: 1px solid rgb(227, 226, 224) !important;
  margin: 1rem 0 !important;
}

/* Images */
.notion-style-editor img {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 3px !important;
  margin: 0.5rem 0 !important;
}

/* Yoopta Image Plugin Styles */
.notion-style-editor .yoopta-image {
  margin: 1rem 0 !important;
  border-radius: 6px !important;
  overflow: hidden !important;
}

.notion-style-editor .yoopta-image-uploader {
  border: 2px dashed rgb(209, 213, 219) !important;
  border-radius: 6px !important;
  padding: 2rem !important;
  text-align: center !important;
  background: rgb(249, 250, 251) !important;
  transition: all 0.2s ease !important;
}

.notion-style-editor .yoopta-image-uploader:hover {
  border-color: rgb(59, 130, 246) !important;
  background: rgb(239, 246, 255) !important;
}

.notion-style-editor .yoopta-image-placeholder {
  color: rgb(107, 114, 128) !important;
  font-size: 14px !important;
}

.notion-style-editor .yoopta-image-embed-input {
  width: 100% !important;
  padding: 0.5rem !important;
  border: 1px solid rgb(209, 213, 219) !important;
  border-radius: 4px !important;
  font-size: 14px !important;
}

.notion-style-editor .yoopta-image-embed-input:focus {
  outline: none !important;
  border-color: rgb(59, 130, 246) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Links */
.notion-style-editor a {
  color: rgb(46, 170, 220) !important;
  text-decoration: underline !important;
  text-decoration-color: rgba(46, 170, 220, 0.4) !important;
  transition: text-decoration-color 0.1s ease !important;
}

.notion-style-editor a:hover {
  text-decoration-color: rgb(46, 170, 220) !important;
}

/* Callouts */
.notion-style-editor .yoopta-callout {
  background: rgb(241, 245, 249) !important;
  border: 1px solid rgb(226, 232, 240) !important;
  border-radius: 3px !important;
  padding: 1rem !important;
  margin: 0.5rem 0 !important;
}

/* Tables */
.notion-style-editor table {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: 0.5rem 0 !important;
}

.notion-style-editor th,
.notion-style-editor td {
  border: 1px solid rgb(227, 226, 224) !important;
  padding: 0.5rem !important;
  text-align: left !important;
}

.notion-style-editor th {
  background: rgb(247, 246, 243) !important;
  font-weight: 600 !important;
}

/* Action Menu (Slash Commands) - Consolidated and Enhanced */
.yoopta-action-menu {
  background: white !important;
  border: 1px solid rgba(55, 53, 47, 0.16) !important;
  border-radius: 6px !important;
  box-shadow: rgba(15, 15, 15, 0.05) 0px 0px 0px 1px, rgba(15, 15, 15, 0.1) 0px 3px 6px, rgba(15, 15, 15, 0.2) 0px 9px 24px !important;
  padding: 4px !important;
  max-height: 300px !important;
  overflow-y: auto !important;
  z-index: 1000 !important;
  min-width: 200px !important;
}

.yoopta-action-menu-item {
  padding: 8px 12px !important;
  border-radius: 3px !important;
  color: rgb(55, 53, 47) !important;
  font-size: 14px !important;
  transition: background-color 0.1s ease !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
  margin: 1px 0 !important;
}

.yoopta-action-menu-item:hover,
.yoopta-action-menu-item-selected,
.yoopta-action-menu-item.selected {
  background: rgba(55, 53, 47, 0.08) !important;
}

.yoopta-action-menu-item-icon {
  width: 16px !important;
  height: 16px !important;
  flex-shrink: 0 !important;
  color: rgba(55, 53, 47, 0.8) !important;
}

.yoopta-action-menu-item-text {
  flex: 1 !important;
  font-weight: 500 !important;
}

.yoopta-action-menu-item-description {
  font-size: 12px !important;
  color: rgba(55, 53, 47, 0.6) !important;
  margin-top: 2px !important;
}

/* Toolbar */
.yoopta-toolbar {
  background: white !important;
  border: 1px solid rgb(227, 226, 224) !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  padding: 0.25rem !important;
  display: flex !important;
  gap: 0.25rem !important;
}

.yoopta-toolbar-button {
  padding: 0.375rem !important;
  border-radius: 3px !important;
  border: none !important;
  background: transparent !important;
  cursor: pointer !important;
  transition: background-color 0.1s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.yoopta-toolbar-button:hover {
  background: rgb(247, 246, 243) !important;
}

.yoopta-toolbar-button-active {
  background: rgb(46, 170, 220) !important;
  color: white !important;
}

/* Dark theme toolbar and action menu support */
.dark .yoopta-toolbar,
.dark .yoopta-action-menu {
  background: #252525 !important; /* Use app's dark background */
  border: 1px solid #434343 !important; /* Use app's dark border */
  color: #f7f7f7 !important; /* Use app's dark foreground */
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 0px 1px, rgba(0, 0, 0, 0.3) 0px 4px 8px, rgba(0, 0, 0, 0.5) 0px 12px 32px !important;
}

.dark .yoopta-toolbar-button {
  color: #f7f7f7 !important;
}

.dark .yoopta-toolbar-button:hover {
  background: #434343 !important; /* Use app's muted color for hover */
}

.dark .yoopta-toolbar-button-active {
  background: #3861fb !important; /* Use app's primary color */
  color: white !important;
}

/* Dark theme action menu items */
.dark .yoopta-action-menu-item {
  color: #f7f7f7 !important; /* High contrast white text */
}

.dark .yoopta-action-menu-item:hover,
.dark .yoopta-action-menu-item-selected,
.dark .yoopta-action-menu-item.selected {
  background: #434343 !important; /* Use app's muted color for hover */
  color: #ffffff !important; /* Pure white for selected state */
}

.dark .yoopta-action-menu-item-description {
  color: #b3b3b3 !important; /* Use app's muted foreground */
}

.dark .yoopta-action-menu-item-icon {
  color: #f7f7f7 !important; /* High contrast for icons */
}

.dark .yoopta-action-menu-item-text {
  color: #f7f7f7 !important; /* Ensure text is always visible */
}

/* Placeholder text */
.notion-style-editor .yoopta-placeholder {
  color: rgb(156, 163, 175) !important;
  font-style: normal !important;
}

/* Dark mode support - using class-based dark mode */
.dark .notion-style-editor {
  color: rgb(229, 231, 235) !important;
}

.dark .notion-style-editor .yoopta-block:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

.dark .notion-style-editor h1,
.dark .notion-style-editor h2,
.dark .notion-style-editor h3,
.dark .notion-style-editor h4,
.dark .notion-style-editor h5,
.dark .notion-style-editor h6,
.dark .notion-style-editor p,
.dark .notion-style-editor span,
.dark .notion-style-editor div {
  color: rgb(229, 231, 235) !important;
}

.dark .notion-style-editor pre {
  background: rgb(31, 41, 55) !important;
  border-color: rgb(55, 65, 81) !important;
  color: rgb(229, 231, 235) !important;
}

.dark .notion-style-editor code {
  background: rgba(255, 255, 255, 0.1) !important;
  color: rgb(229, 231, 235) !important;
}

.dark .notion-style-editor .yoopta-placeholder {
  color: rgb(107, 114, 128) !important;
}

.dark .notion-style-editor blockquote {
  border-left-color: rgba(229, 231, 235, 0.3) !important;
  color: rgb(229, 231, 235) !important;
}

.dark .notion-style-editor a {
  color: rgb(96, 165, 250) !important;
}

.dark .notion-style-editor a:hover {
  color: rgb(147, 197, 253) !important;
}

/* Legacy support for prefers-color-scheme */
@media (prefers-color-scheme: dark) {
  .notion-style-editor {
    color: rgb(229, 231, 235) !important;
  }

  .notion-style-editor .yoopta-block:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  .notion-style-editor h1,
  .notion-style-editor h2,
  .notion-style-editor h3,
  .notion-style-editor h4,
  .notion-style-editor h5,
  .notion-style-editor h6,
  .notion-style-editor p,
  .notion-style-editor span,
  .notion-style-editor div {
    color: rgb(229, 231, 235) !important;
  }

  .notion-style-editor pre {
    background: rgb(31, 41, 55) !important;
    border-color: rgb(55, 65, 81) !important;
    color: rgb(229, 231, 235) !important;
  }

  .notion-style-editor code {
    background: rgba(255, 255, 255, 0.1) !important;
    color: rgb(229, 231, 235) !important;
  }

  .notion-style-editor .yoopta-placeholder {
    color: rgb(107, 114, 128) !important;
  }

  .notion-style-editor blockquote {
    border-left-color: rgba(229, 231, 235, 0.3) !important;
    color: rgb(229, 231, 235) !important;
  }

  .notion-style-editor a {
    color: rgb(96, 165, 250) !important;
  }

  .notion-style-editor a:hover {
    color: rgb(147, 197, 253) !important;
  }
}

/* Focus states */
.notion-style-editor .yoopta-block-focused {
  outline: none !important;
}

/* Selection styling */
.notion-style-editor ::selection {
  background: rgba(46, 170, 220, 0.2) !important;
}

/* Smooth animations */
.notion-style-editor * {
  transition: background-color 0.1s ease, color 0.1s ease !important;
}

/* Keyboard interaction improvements */
.notion-style-editor {
  /* Ensure proper keyboard navigation */
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Full width enforcement for all block elements */
.notion-style-editor > *,
.notion-style-editor .yoopta-block > *,
.notion-style-editor [data-yoopta-block] > *,
.notion-style-editor [data-yoopta-element] {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Ensure proper line break behavior */
.notion-style-editor br {
  line-height: 1.3 !important;
  display: block !important;
  content: "" !important;
  margin: 0 !important;
}

/* Handle empty blocks properly for keyboard navigation */
.notion-style-editor [contenteditable="true"]:empty {
  min-height: 1.3em !important;
}

.notion-style-editor [contenteditable="true"]:empty::before {
  content: "" !important;
  display: inline-block !important;
  width: 0 !important;
  height: 1.3em !important;
}

/* Override any potential width constraints from Yoopta's default styles */
.notion-style-editor .yoopta-block-wrapper,
.notion-style-editor .yoopta-block-content,
.notion-style-editor .yoopta-element,
.notion-style-editor .yoopta-element-wrapper,
.notion-style-editor [data-yoopta-block-wrapper],
.notion-style-editor [data-yoopta-block-content],
.notion-style-editor [data-yoopta-element],
.notion-style-editor [data-yoopta-element-wrapper] {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Ensure all text elements take full width */
.notion-style-editor span,
/* .notion-style-editor div, */
.notion-style-editor section,
.notion-style-editor article {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Special handling for inline elements that should still be inline */
.notion-style-editor strong,
.notion-style-editor em,
.notion-style-editor code,
.notion-style-editor a,
.notion-style-editor mark {
  width: auto !important;
  max-width: none !important;
  display: inline !important;
}

/* Dark theme support for inline elements */
.dark .notion-style-editor strong,
.dark .notion-style-editor em,
.dark .notion-style-editor b,
.dark .notion-style-editor i {
  color: rgb(229, 231, 235) !important;
}

.dark .notion-style-editor mark {
  background-color: rgba(255, 235, 59, 0.3) !important;
  color: rgb(229, 231, 235) !important;
}

/* Dark theme support for lists */
.dark .notion-style-editor ul,
.dark .notion-style-editor ol,
.dark .notion-style-editor li {
  color: rgb(229, 231, 235) !important;
}

/* Dark theme support for tables */
.dark .notion-style-editor table,
.dark .notion-style-editor th,
.dark .notion-style-editor td {
  color: rgb(229, 231, 235) !important;
  border-color: rgba(229, 231, 235, 0.2) !important;
}

.dark .notion-style-editor th {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

/* Dark theme support for callouts and special blocks */
.dark .notion-style-editor .yoopta-callout,
.dark .notion-style-editor [data-yoopta-callout] {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(229, 231, 235, 0.2) !important;
  color: rgb(229, 231, 235) !important;
}

/* Dark theme support for dividers */
.dark .notion-style-editor hr,
.dark .notion-style-editor .yoopta-divider {
  border-color: rgba(229, 231, 235, 0.2) !important;
}

/* Ensure all text nodes inherit dark theme color */
.dark .notion-style-editor * {
  color: inherit !important;
}

/* Override any specific text color that might be set */
.dark .notion-style-editor [data-yoopta-block] * {
  color: rgb(229, 231, 235) !important;
}

/* Embed and Video block styling */
.notion-style-editor .yoopta-embed,
.notion-style-editor .yoopta-video,
.notion-style-editor [data-yoopta-embed],
.notion-style-editor [data-yoopta-video] {
  width: 100% !important;
  max-width: 100% !important;
  margin: 1rem 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.notion-style-editor .yoopta-embed iframe,
.notion-style-editor .yoopta-video iframe,
.notion-style-editor .yoopta-embed video,
.notion-style-editor .yoopta-video video {
  width: 100% !important;
  height: auto !important;
  min-height: 315px !important;
  border: none !important;
  border-radius: 8px !important;
}

/* Dark theme for embeds and videos */
.dark .notion-style-editor .yoopta-embed,
.dark .notion-style-editor .yoopta-video,
.dark .notion-style-editor [data-yoopta-embed],
.dark .notion-style-editor [data-yoopta-video] {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(229, 231, 235, 0.1) !important;
}

/* Link options modal styling */
.link-options-modal {
  z-index: 9999 !important;
}

/* Ensure proper spacing for new blocks */
.notion-style-editor .yoopta-block + .yoopta-block {
  margin-top: 0.5rem !important;
}

/* Toolbar styling - clean like Notion */
.yoopta-toolbar {
  background: white !important;
  border: 1px solid rgba(55, 53, 47, 0.16) !important;
  border-radius: 6px !important;
  box-shadow: rgba(15, 15, 15, 0.05) 0px 0px 0px 1px, rgba(15, 15, 15, 0.1) 0px 3px 6px, rgba(15, 15, 15, 0.2) 0px 9px 24px !important;
  padding: 4px !important;
}

.yoopta-toolbar button {
  border: none !important;
  background: transparent !important;
  border-radius: 3px !important;
  padding: 6px 8px !important;
  color: rgb(55, 53, 47) !important;
  font-size: 14px !important;
  transition: background-color 0.1s ease !important;
}

.yoopta-toolbar button:hover {
  background: rgba(55, 53, 47, 0.08) !important;
}

/* Remove duplicate action menu styles - consolidated above */

/* Dark theme for action menu - comprehensive coverage with consistent colors */
.dark .yoopta-action-menu {
  background: #252525 !important; /* App's dark background */
  border: 1px solid #434343 !important; /* App's dark border */
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 0px 1px, rgba(0, 0, 0, 0.3) 0px 4px 8px, rgba(0, 0, 0, 0.5) 0px 12px 32px !important;
}

.dark .yoopta-action-menu-item {
  color: #f7f7f7 !important; /* High contrast text */
}

.dark .yoopta-action-menu-item:hover,
.dark .yoopta-action-menu-item.selected,
.dark .yoopta-action-menu-item-selected {
  background: #434343 !important; /* App's muted color for hover */
  color: #ffffff !important; /* Pure white for selected state */
}

.dark .yoopta-action-menu-item-icon {
  color: #f7f7f7 !important; /* High contrast for icons */
}

.dark .yoopta-action-menu-item-text {
  color: #f7f7f7 !important; /* High contrast text */
}

.dark .yoopta-action-menu-item-description {
  color: #b3b3b3 !important; /* App's muted foreground */
}

/* Additional selectors for different action menu implementations */
.dark [data-yoopta-action-menu],
.dark .action-menu,
.dark .slash-menu,
.dark .command-menu {
  background: #252525 !important; /* App's dark background */
  border: 1px solid #434343 !important; /* App's dark border */
  color: #f7f7f7 !important; /* High contrast text */
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 0px 1px, rgba(0, 0, 0, 0.3) 0px 4px 8px, rgba(0, 0, 0, 0.5) 0px 12px 32px !important;
}

.dark [data-yoopta-action-menu] .menu-item,
.dark .action-menu .menu-item,
.dark .slash-menu .menu-item,
.dark .command-menu .menu-item {
  color: #f7f7f7 !important; /* High contrast text */
}

.dark [data-yoopta-action-menu] .menu-item:hover,
.dark .action-menu .menu-item:hover,
.dark .slash-menu .menu-item:hover,
.dark .command-menu .menu-item:hover {
  background: #434343 !important; /* App's muted color for hover */
  color: #ffffff !important; /* Pure white for hover state */
}

/* Additional dark mode fixes for slash menu edge cases */
.dark .yoopta-action-menu * {
  border-color: #434343 !important; /* Ensure all borders use dark theme */
}

.dark .yoopta-action-menu-item-title,
.dark .yoopta-action-menu-item-label {
  color: #f7f7f7 !important; /* Ensure all text elements are visible */
}

.dark .yoopta-action-menu-item:focus,
.dark .yoopta-action-menu-item:focus-visible {
  background: #434343 !important; /* Focus state styling */
  color: #ffffff !important;
  outline: 2px solid #3861fb !important; /* Use app's primary color for focus outline */
  outline-offset: -2px !important;
}

/* Ensure scrollbar is visible in dark mode */
.dark .yoopta-action-menu::-webkit-scrollbar {
  width: 6px !important;
}

.dark .yoopta-action-menu::-webkit-scrollbar-track {
  background: #252525 !important;
}

.dark .yoopta-action-menu::-webkit-scrollbar-thumb {
  background: #434343 !important;
  border-radius: 3px !important;
}

.dark .yoopta-action-menu::-webkit-scrollbar-thumb:hover {
  background: #707070 !important;
}

/* Additional action menu classes that might be generated by Yoopta library */
.dark .yoopta-action-menu-list,
.dark .yoopta-action-menu-list-content,
.dark .yoopta-action-menu-content,
.dark .yoopta-action-menu-container,
.dark .yoopta-action-menu-wrapper {
  background: #252525 !important; /* App's dark background */
  border: 1px solid #434343 !important; /* App's dark border */
  color: #f7f7f7 !important; /* High contrast text */
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 0px 1px, rgba(0, 0, 0, 0.3) 0px 4px 8px, rgba(0, 0, 0, 0.5) 0px 12px 32px !important;
}

/* Ensure all child elements in action menu list content inherit dark styling */
.dark .yoopta-action-menu-list-content *,
.dark .yoopta-action-menu-content *,
.dark .yoopta-action-menu-container *,
.dark .yoopta-action-menu-wrapper * {
  color: #f7f7f7 !important;
  border-color: #434343 !important;
}

/* Action menu list items - comprehensive coverage */
.dark .yoopta-action-menu-list .yoopta-action-menu-item,
.dark .yoopta-action-menu-list-content .yoopta-action-menu-item,
.dark .yoopta-action-menu-content .yoopta-action-menu-item {
  color: #f7f7f7 !important;
  background: transparent !important;
}

.dark .yoopta-action-menu-list .yoopta-action-menu-item:hover,
.dark .yoopta-action-menu-list-content .yoopta-action-menu-item:hover,
.dark .yoopta-action-menu-content .yoopta-action-menu-item:hover,
.dark .yoopta-action-menu-list .yoopta-action-menu-item.selected,
.dark .yoopta-action-menu-list-content .yoopta-action-menu-item.selected,
.dark .yoopta-action-menu-content .yoopta-action-menu-item.selected {
  background: #434343 !important;
  color: #ffffff !important;
}

/* Data attribute selectors for action menu components */
.dark [data-yoopta-action-menu-list],
.dark [data-yoopta-action-menu-content],
.dark [data-yoopta-action-menu-container],
.dark [data-action-menu],
.dark [data-action-menu-list],
.dark [data-action-menu-content] {
  background: #252525 !important;
  border: 1px solid #434343 !important;
  color: #f7f7f7 !important;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 0px 1px, rgba(0, 0, 0, 0.3) 0px 4px 8px, rgba(0, 0, 0, 0.5) 0px 12px 32px !important;
}

/* Catch-all for any action menu related elements */
.dark [class*="yoopta-action-menu"],
.dark [class*="action-menu"],
.dark [class*="slash-menu"] {
  background: #252525 !important;
  border-color: #434343 !important;
  color: #f7f7f7 !important;
}

/* Catch-all for action menu items */
.dark [class*="yoopta-action-menu"] [class*="item"],
.dark [class*="action-menu"] [class*="item"],
.dark [class*="slash-menu"] [class*="item"] {
  color: #f7f7f7 !important;
}

.dark [class*="yoopta-action-menu"] [class*="item"]:hover,
.dark [class*="action-menu"] [class*="item"]:hover,
.dark [class*="slash-menu"] [class*="item"]:hover,
.dark [class*="yoopta-action-menu"] [class*="item"].selected,
.dark [class*="action-menu"] [class*="item"].selected,
.dark [class*="slash-menu"] [class*="item"].selected {
  background: #434343 !important;
  color: #ffffff !important;
}

/* Ensure scrollable content areas within action menus are properly styled */
.dark .yoopta-action-menu-list-content::-webkit-scrollbar,
.dark .yoopta-action-menu-content::-webkit-scrollbar,
.dark [class*="action-menu"]::-webkit-scrollbar {
  width: 6px !important;
}

.dark .yoopta-action-menu-list-content::-webkit-scrollbar-track,
.dark .yoopta-action-menu-content::-webkit-scrollbar-track,
.dark [class*="action-menu"]::-webkit-scrollbar-track {
  background: #252525 !important;
}

.dark .yoopta-action-menu-list-content::-webkit-scrollbar-thumb,
.dark .yoopta-action-menu-content::-webkit-scrollbar-thumb,
.dark [class*="action-menu"]::-webkit-scrollbar-thumb {
  background: #434343 !important;
  border-radius: 3px !important;
}

.dark .yoopta-action-menu-list-content::-webkit-scrollbar-thumb:hover,
.dark .yoopta-action-menu-content::-webkit-scrollbar-thumb:hover,
.dark [class*="action-menu"]::-webkit-scrollbar-thumb:hover {
  background: #707070 !important;
}

/* Link click behavior fixes - disable hover popups and tooltips */
.notion-style-editor a[href] {
  /* Ensure links are clickable but don't show tooltips */
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Disable any link hover popups, tooltips, or preview cards */
.notion-style-editor a[href]:hover {
  /* Remove any title attributes that might show browser tooltips */
  position: relative !important;
}

/* Hide all possible Yoopta link tool popups and tooltips */
.yoopta-link-tool,
[data-yoopta-link-tool],
.link-tool-popup,
.link-preview,
.link-tooltip,
.yoopta-link-preview,
.yoopta-link-popup,
.yoopta-link-hover,
[data-link-preview],
[data-link-tooltip] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
  z-index: -9999 !important;
}

/* Ensure no hover effects create popups */
.notion-style-editor a[href]:hover::before,
.notion-style-editor a[href]:hover::after {
  display: none !important;
  content: none !important;
}

/* Remove any potential tooltip containers */
.notion-editor-container .tooltip,
.notion-editor-container [data-tooltip],
.notion-editor-container .popover,
.notion-editor-container [data-popover] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Disable browser's default link preview on hover */
.notion-style-editor a[href] {
  text-decoration: underline !important;
  text-decoration-color: rgba(46, 170, 220, 0.4) !important;
  transition: text-decoration-color 0.1s ease !important;
}

.notion-style-editor a[href]:hover {
  text-decoration-color: rgb(46, 170, 220) !important;
  /* Explicitly prevent any popup behavior */
  position: relative !important;
  z-index: auto !important;
}
