/**
 * Image upload utility for YooptaEditor
 * Provides client-side validation and helper functions for image processing
 * API calls are handled by Redux sagas
 */

export interface ImageValidationResult {
  isValid: boolean;
  error?: string;
}

export interface ImageInfo {
  name: string;
  size: number;
  type: string;
  extension: string;
  formattedSize: string;
}

/**
 * Validate if a file is a valid image for upload
 */
export function validateImageFile(file: File): ImageValidationResult {
  // Validate file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Invalid file type. Only JPG, PNG, GIF, and WebP images are allowed.'
    };
  }

  // Validate file size (5MB limit)
  const maxSize = 5 * 1024 * 1024; // 5MB in bytes
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File size too large. Maximum size is 5MB.'
    };
  }

  // Additional validation
  if (file.size === 0) {
    return {
      isValid: false,
      error: 'File is empty.'
    };
  }

  return { isValid: true };
}

/**
 * Get detailed information about an image file
 */
export function getImageInfo(file: File): ImageInfo {
  return {
    name: file.name,
    size: file.size,
    type: file.type,
    extension: getFileExtension(file.name),
    formattedSize: formatFileSize(file.size),
  };
}



/**
 * Simple boolean check if a file is a valid image (backward compatibility)
 */
export function isValidImageFile(file: File): boolean {
  const validation = validateImageFile(file);
  return validation.isValid;
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Extract file extension from filename
 */
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}

/**
 * Generate a unique filename with timestamp
 */
export function generateUniqueFilename(originalName: string): string {
  const timestamp = Date.now();
  const extension = getFileExtension(originalName);
  const baseName = originalName.replace(/\.[^/.]+$/, '');
  
  return `${timestamp}-${baseName}.${extension}`;
}
