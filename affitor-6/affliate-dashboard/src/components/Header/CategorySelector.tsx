import { affiliateActions, categoryActions, filterActions } from "@/features/rootActions";
import { ICategory } from "@/interfaces";
import { useDispatch, useSelector } from "react-redux";
import router from "next/router";
import { selectFilters } from "@/features/filter/filter.slice";

export default function CategorySelector({
  categories,
}: {
  categories: ICategory[];
}) {
  const dispatch = useDispatch();
  const filters = useSelector(selectFilters);
  
  return (
    <div
      className="md:absolute top-full left-0 md:w-48 md:rounded-lg shadow-lg 
             bg-primary transform transition-all duration-300 ease-in-out translate-y-4 w-full"
    >
      <div className="md:pt-2">
        {categories.map((category: ICategory) => (
            <div
            key={`cat-${category.documentId || category.id}`}
            className={`relative px-4 py-2 flex items-center
              cursor-pointer pl-[10px]
              h-[50px] border-b-[0.5px] border-gray-200  
              after:absolute after:left-1/2 after:-translate-x-1/2 after:bottom-0 after:h-[2px] 
              after:bg-blue-500 after:w-0 transition-all after:duration-300 hover:after:w-full
              ${filters.category === category.id ? 'font-semibold text-blue-500' : ''}
            `}
            onClick={() => {
              // Clear existing affiliate data
              dispatch(affiliateActions.setAffiliates(null));
              
              // Update BOTH states to keep them in sync - the category state and filter state
              // dispatch(categoryActions.setCurrentCategory(category));
              dispatch(filterActions.setActiveCategory(category));
              
              // Navigate to main page if not already there
              if (router.pathname !== '/affiliates') 
                router.push('/')
            }}
            >
            {category.name}
            </div>
        ))}
      </div>
    </div>
  );
}
