import Link from "next/link";
import React, { ReactNode } from "react";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";

interface LinkHeaderProps {
  label?: string;
  href: string;
  activeNav?: string;
  setActiveNav?: (nav: string) => void;
  children?: ReactNode;
  onClick?: (e: React.MouseEvent) => void; // Add optional onClick handler
  isDropdown?: boolean; // Add flag for dropdown links
}

const LinkHeader: React.FC<LinkHeaderProps> = ({
  label,
  href,
  activeNav,
  setActiveNav,
  children,
  onClick,
  isDropdown = false,
}) => {
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      // If custom onClick is provided, call it and let it handle navigation
      onClick(e);
    } else if (setActiveNav && label) {
      // Otherwise handle default navigation behavior
      setActiveNav(label);
    }

    // For dropdown links with children, prevent default navigation
    if (isDropdown && children) {
      e.preventDefault();
    }
  };

  const isActive = activeNav === label;
  const activeLinkClasses = isActive
    ? "font-medium"
    : "text-primary-foreground hover:text-primary-foreground";

  // If it's a dropdown with children, wrap in span instead of Link
  if (isDropdown && children) {
    return (
      <div className={`${activeLinkClasses} cursor-pointer`}>
        {children || label}
      </div>
    );
  }

  // Regular link
  return (
    <Link href={href} className={activeLinkClasses} onClick={handleClick}>
      {children || label}
    </Link>
  );
};

export default LinkHeader;
