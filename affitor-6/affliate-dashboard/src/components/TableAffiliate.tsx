"use client";

import { useEffect, useMemo, useState, useRef } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  OnChangeFn,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "@/components/ui/pagination";
import ColumnsModal from "./ColumnsModal";
import { IAffiliate } from "@/interfaces";
import PaymentMethod from "./PaymentMethod";
import { NumberFormat } from "./NumberFormat";
import _ from "lodash";
import { affiliateActions } from "@/features/rootActions";
import { useDispatch, useSelector } from "react-redux";
import { ArrowUp, ArrowDown, Info, ArrowUpRight } from "lucide-react";
import {
  selectLoadingAffiliateUrlId, selectAffiliateUrlById
} from "@/features/affiliate/affiliate.slice";

interface DataTableProps<TData, TValue> {
  data: TData[];
  pageSize?: number;
  isPagination?: boolean;
  sorting?: SortingState;
  setSorting?: (value: SortingState) => void;
  industryPath?: string;
  tableId?: string;
  forceHideColumns?: string[];
}

// Visit Button Component
interface VisitButtonProps {
  affiliateId: string;
}

const VisitButton: React.FC<VisitButtonProps> = ({ affiliateId }) => {
  const dispatch = useDispatch();
  const loadingUrlId = useSelector(selectLoadingAffiliateUrlId);
  const cachedUrl = useSelector((state: any) => selectAffiliateUrlById(state, affiliateId));
  const isLoading = loadingUrlId === affiliateId;

  // Fetch URL on component mount if not already cached
  useEffect(() => {
    if (!cachedUrl && !isLoading) {
      dispatch(
        affiliateActions.fetchAffiliateUrl({
          id: affiliateId,
          shouldOpen: false, // Don't auto-open when caching
        })
      );
    }
  }, [cachedUrl, isLoading, affiliateId, dispatch]);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // If URL is cached, let the anchor handle navigation
    // If not cached, fetch it (which will trigger auto-opening via Header effect)
    if (!cachedUrl) {
      dispatch(
        affiliateActions.fetchAffiliateUrl({
          id: affiliateId,
          shouldOpen: true, // Auto-open when clicking
        })
      );
    }
  };

  // Show loading state if loading or no URL cached yet
  if (isLoading || !cachedUrl) {
    return (
      <div className="inline-flex items-center bg-white rounded-full text-sm font-medium shadow-lg border-2 border-slate-300 opacity-70 px-3 py-1.5">
        <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
      </div>
    );
  }

  // Render as anchor tag when URL is available
  return (
    <a
      href={cachedUrl}
      onClick={handleClick}
      className="inline-flex items-center bg-white rounded-full text-sm font-medium shadow-lg border-2 border-slate-300 transition-all duration-200 transform hover:scale-105 hover:shadow-xl focus:outline-none no-underline"
      target="_self"
    >
      <div className="flex items-center justify-center bg-blue-500 rounded-full p-1.5 m-1">
        <ArrowUpRight
          className="w-3.5 h-3.5 text-white"
          strokeWidth={2.5}
        />
      </div>
      <span className="ml-1 mr-3 font-bold text-slate-800">
        Visit
      </span>
    </a>
  );
};

export function TableAffiliate<TData, TValue>({
  data,
  pageSize = 15,
  isPagination = true,
  sorting,
  setSorting,
  industryPath = "vertical",
  tableId = "default",
  forceHideColumns = [],
}: DataTableProps<TData, TValue>) {
  const [currentPage, setCurrentPage] = useState(0);
  const tableRef = useRef<HTMLDivElement>(null);

  // Move these up before they're used in useMemo
  const dispatch = useDispatch();





  // Remove the custom right-click handler to allow browser's default context menu
  // Original custom right-click handler was preventing default behavior
  const handleRowClick = (e: React.MouseEvent, row: any) => {
    if (e.button === 0) {
      // Left mouse button
      // Determine the correct industry path for this specific row
      const rowIndustryPath = row.original?.industry?.slug || industryPath;
      console.log("Navigation using industry path:", rowIndustryPath);
      window.location.href = `/${rowIndustryPath}/${
        row.original?.slug || row.original?.documentId
      }`;
    }
    // No longer intercepting right-click events
  };

  // Default columns configuration with sortable property
  const defaultColumns = [
    { id: "name", label: "Name", enabled: true, sortable: true },
    {
      id: "avg_conversion",
      label: "EPU",
      enabled: true,
      sortable: true,
    },
    {
      id: "commission.title",
      label: "Commission",
      enabled: true,
      sortable: true,
    },
    {
      id: "monthly_traffic",
      label: "Monthly Traffic",
      enabled: true,
      sortable: true,
    },
    { id: "recurring", label: "Recurring", enabled: true, sortable: true },
    { id: "pricing", label: "Pricing", enabled: true, sortable: true },
    { id: "launch_year", label: "Launch Year", enabled: true, sortable: true },
    {
      id: "payment_methods",
      label: "Payment Methods",
      enabled: false,
      sortable: false,
    },
    {
      id: "cookie_duration",
      label: "Cookies Duration",
      enabled: false,
      sortable: true,
    },
    {
      id: "contact_info",
      label: "Contact Information",
      enabled: false,
      sortable: false,
    },
    { id: "country", label: "Country", enabled: true, sortable: true },
    { id: "action", label: "Action", enabled: true, sortable: false },
  ];

  const storageKey = `affiliateTableColumns_${tableId}`;

  // Initialize enableColumns with localStorage value or default
  const getInitialColumns = () => {
    try {
      const savedColumns = localStorage.getItem(storageKey);
      let columns = defaultColumns;
      if (savedColumns) {
        const parsedColumns = JSON.parse(savedColumns);
        columns = defaultColumns.map((defaultCol) => {
          const savedCol = parsedColumns.find(
            (col: any) => col.id === defaultCol.id
          );
          return savedCol || defaultCol;
        });
      }
      // Override with forceHideColumns
      if (forceHideColumns.length > 0) {
        columns = columns.map((col) =>
          forceHideColumns.includes(col.id) ? { ...col, enabled: false } : col
        );
      }
      return columns;
    } catch (error) {
      console.error("Error reading saved column preferences:", error);
    }
    return defaultColumns;
  };

  const [enableColumns, setEnableColumns] = useState(getInitialColumns);
  const [defColumns, setDefColumns] = useState<ColumnDef<IAffiliate>[]>([]);

  // Save column configuration to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(storageKey, JSON.stringify(enableColumns));
    } catch (error) {
      console.error("Error saving column preferences:", error);
    }
  }, [enableColumns, storageKey]);

  const defColumnsMemo = useMemo(() => {
    return [
      {
        accessorKey: "index",
        header: "#",
        cell: (info: any) => info.row.index + 1,
        enableSorting: false, // Disable sorting for this column
      },
      {
        accessorKey: "name",
        header: "Name",
        cell: (info: any) => {
          const program: IAffiliate = info.row.original;
          return (
            <div
              className="flex items-center gap-1 sm:gap-3 w-[125px] md:w-auto md:min-w-[120px]"
              style={{ overflowWrap: "anywhere" }}
            >
              <div className="w-8 h-8 sm:w-10 sm:h-10 min-w-8 sm:min-w-10 relative">
                <img
                  src={_.get(
                    program,
                    "image.url",
                    "https://example.com/default-image.jpg"
                  )}
                  alt={program.name}
                  className="w-full h-full object-contain"
                />
              </div>
              <div>
                <div className="font-medium text-[12px] sm:text-base whitespace-wrap text-slate-800 dark:text-white">
                  {program.name}
                </div>
                <div className="text-[10px] sm:text-xs text-gray-500 dark:text-gray-400">
                  {program.categories && program.categories.length > 0
                    ? _.get(program.categories[0], "name", "")
                    : ""}
                </div>
              </div>
            </div>
          );
        },
        enableSorting: true, // Enable sorting for this column
      },
      {
        accessorKey: "avg_conversion",
        header: () => (
          <div className="flex items-center gap-1">
            <span>EPU</span>
            <div
              className="relative group"
              onClick={(e) => {
                e.stopPropagation(); // Prevent triggering column header click event
              }}
            >
              <Info
                size={14}
                className="text-gray-400 dark:text-gray-500 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  // Toggle tooltip visibility for mobile
                  const tooltip = e.currentTarget.nextElementSibling;
                  if (tooltip) {
                    tooltip.classList.toggle("opacity-0");
                    tooltip.classList.toggle("opacity-100");
                  }
                }}
              />
              <div className="fixed sm:absolute left-1/2 sm:left-1/2 top-0 transform -translate-x-1/2 -translate-y-[150%] sm:-translate-y-[120%] sm:mb-2 px-3 py-2 bg-primary dark:bg-slate-800 text-primary-foreground dark:text-slate-200 text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-[9999] shadow-md w-auto max-w-[90vw] sm:max-w-none text-center">
                Earning Potential per User
                <div className="block absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 rotate-45 bg-transparent"></div>
              </div>
            </div>
          </div>
        ),
        cell: (info: any) => {
          const program: IAffiliate = info.row.original;
          return (
            <div className="text-sm text-foreground">
              {(() => {
                const currencySymbols: Record<string, string> = {
                  USD: "$",
                  EUR: "€",
                };
                const symbol = program.currency
                  ? currencySymbols[program.currency] || program.currency
                  : "$";
                return `${symbol}${(
                  program.avg_conversion || 0
                ).toLocaleString()}`;
              })()}
            </div>
          );
        },
        enableSorting: true,
      },
      {
        accessorKey: "commission.title",
        header: "Commission",
        cell: (info: any) => (
          <div className="text-sm text-foreground">
            {info.getValue() as React.ReactNode}
          </div>
        ),
        enableSorting: true,
      },
      {
        accessorKey: "monthly_traffic",
        header: "Monthly Traffic",
        cell: (info: any) => (
          <div className="text-sm text-foreground">
            {info.getValue() ? (
              <>
                <NumberFormat value={info.getValue() as number} />
                <span className="text-xs text-gray-500"> /mo</span>
              </>
            ) : (
              "N/A"
            )}
          </div>
        ),
        enableSorting: true,
      },
      {
        accessorKey: "recurring",
        header: "Recurring",
        cell: (info: any) => {
          const program: IAffiliate = info.row.original;
          return (
            <div className="text-sm text-foreground">
              {program.recurring?.replace("In", "") || "_"}
            </div>
          );
        },
        enableSorting: true,
      },
      {
        accessorKey: "pricing",
        header: "Pricing",
        cell: (info: any) => (
          <div className="text-sm text-foreground">
            {info.getValue() as React.ReactNode}
          </div>
        ),
        enableSorting: true,
      },
      {
        accessorKey: "launch_year",
        header: "Launch Year",
        cell: (info: any) => (
          <div className="text-sm text-foreground">
            {info.getValue() as React.ReactNode}
          </div>
        ),
        enableSorting: true,
      },
      {
        accessorKey: "cookie_duration",
        header: "Cookie Duration",
        cell: (info: any) => {
          const program: IAffiliate = info.row.original;
          return (
            <span>
              {program.cookies_duration
                ? program.cookies_duration + " days"
                : "-"}
            </span>
          );
        },
        enableSorting: true, // Enable sorting for this column
      },
      {
        accessorKey: "contact_info",
        header: "Contact Info",
        cell: (info: any) => {
          const program: IAffiliate = info.row.original;
          const contactInfo = program.contact_information
            ? program.contact_information
            : "-";

          return (
            <div className="relative group">
              <span
                className="block max-w-[150px] truncate"
                title={contactInfo} // Native browser tooltip
              >
                {contactInfo}
              </span>
              {/* Custom tooltip that shows on hover for better styling control */}
              {contactInfo !== "-" && contactInfo.length > 25 && (
                <div className="absolute z-50 left-0 top-full mt-1 p-2 bg-primary text-primary-foreground text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-md pointer-events-none min-w-[200px] max-w-[300px] whitespace-normal">
                  {contactInfo}
                </div>
              )}
            </div>
          );
        },
        enableSorting: false, // Disable sorting for this column
      },
      {
        accessorKey: "payment_methods",
        header: "Payment Methods",
        cell: (info: any) => {
          const program: IAffiliate = info.row.original;
          const paymentMethods = program.payment_methods;
          return (
            <div className="flex gap-2 flex-wrap items-center">
              {paymentMethods.slice(0, 2).map((method, i) => (
                <PaymentMethod key={i} method={method} />
              ))}
              {program.payment_methods &&
                program.payment_methods.length > 2 && (
                  <div className="relative group">
                    <div className="w-6 h-6 flex items-center justify-center rounded-lg bg-muted cursor-help">
                      <span className="text-xs">
                        +{program.payment_methods.length - 2}
                      </span>
                    </div>
                    {/* Enhanced popover that shows icons and names */}
                    <div className="absolute z-50 left-1/2 -translate-x-1/2 bottom-full mb-2 p-3 bg-primary text-primary-foreground text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-md pointer-events-none min-w-[150px]">
                      <div className="grid gap-2">
                        {program.payment_methods.slice(2).map((method, i) => (
                          <div key={i} className="flex items-center gap-2">
                            <div className="w-6 h-6 flex-shrink-0">
                              {method.image && method.image.url ? (
                                <img
                                  src={method.image.url}
                                  alt={method.name || "Payment method"}
                                  className="w-full h-full object-contain"
                                />
                              ) : (
                                <div className="w-full h-full rounded-full bg-gray-300 flex items-center justify-center">
                                  <span className="text-[8px]">
                                    {method.name?.charAt(0) || "?"}
                                  </span>
                                </div>
                              )}
                            </div>
                            <span>{method.name || "Unknown method"}</span>
                          </div>
                        ))}
                      </div>
                      <div className="absolute top-full left-1/2 -translate-x-1/2 w-2 h-2 bg-primary rotate-45"></div>
                    </div>
                  </div>
                )}
            </div>
          );
        },
        enableSorting: false, // Disable sorting for this column
      },
      {
        accessorKey: "country",
        header: "Country",
        cell: (info: any) => {
          const program: IAffiliate = info.row.original;
          const countryName = program.country
            ? program.country
            : "Not specified";
          return <div className="text-sm text-foreground">{countryName}</div>;
        },
        enableSorting: true, // Enable sorting for this column
      },
      {
        accessorKey: "action",
        header: "Action",
        cell: (info: any) => {
          return <VisitButton affiliateId={info.row.original.documentId} />;
        },
        enableSorting: false,
      },
    ];
  }, [dispatch]);

  useEffect(() => {
    setDefColumns(defColumnsMemo);
  }, [defColumnsMemo]);

  const handleToggleColumn = (id: string) => {
    console.log(`Toggling column: ${id}`);
    setEnableColumns((prev) => {
      const updated = prev.map((col) =>
        col.id === id ? { ...col, enabled: !col.enabled } : col
      );
      return updated;
    });
  };

  const handleSortingChange: OnChangeFn<SortingState> = (updaterOrValue) => {
    if (!setSorting) return;
    const newSorting: SortingState =
      typeof updaterOrValue === "function"
        ? updaterOrValue(sorting || [])
        : updaterOrValue;

    setSorting([...newSorting] as SortingState);
  };

  // New function to handle column header clicks that cycles through sort states
  const handleColumnHeaderClick = (e: React.MouseEvent, columnId: string) => {
    e.stopPropagation();

    if (!setSorting) return;

    // Check current sort state of this column
    const currentSortState = getCurrentSortState(columnId);

    // We'll always have a sort active - either keep the current one or apply a new one
    let newSorting: SortingState = [];

    if (currentSortState === "none") {
      // If this column isn't sorted, apply descending sort (and remove any other sort)
      newSorting = [{ id: columnId, desc: true }];
    } else {
      // If this column is already sorted, just toggle between desc/asc
      newSorting = [{ id: columnId, desc: currentSortState === "asc" }];
    }

    // Apply the new sorting
    setSorting(newSorting);
  };

  // Helper function to get current sort state of a column
  const getCurrentSortState = (columnId: string): "none" | "asc" | "desc" => {
    if (!sorting || sorting.length === 0) return "none";

    const sortEntry = sorting.find((sort) => sort.id === columnId);
    if (!sortEntry) return "none";

    return sortEntry.desc ? "desc" : "asc";
  };

  // Make sure the sortable configuration is applied when filtering columns
  const table = useReactTable({
    data,
    columns: (defColumns as ColumnDef<TData, any>[]).filter((col: any) => {
      const foundColumn = enableColumns.find(
        (item) => item.id === col.accessorKey
      );
      const isEnabled = !!foundColumn?.enabled;
      return isEnabled;
    }),
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: handleSortingChange,
    getSortedRowModel: getSortedRowModel(),
    enableSorting: true, // Global sorting is enabled
    state: {
      sorting,
    },
    initialState: {
      pagination: {
        pageSize: pageSize,
      },
      sorting: sorting,
    },
    // Add this line to ensure no default limit is applied
    manualPagination: true,
    // Add this to ensure we display all available data
    pageCount: Math.ceil(data.length / pageSize),
  });

  return (
    <div className="rounded-md w-full relative" ref={tableRef}>
      <ColumnsModal
        columns={enableColumns}
        handleToggleColumn={handleToggleColumn}
      />
      {/* Table header controls row */}
      <div className="flex items-center justify-between px-2 py-2 bg-secondary border-b border-gray-200">
        {/* Left side: (put Trending/Weekly or other controls here if needed) */}
        <div />
        {/* Right side: Customize Columns button */}
        {/* <CustomizeColumnsButton /> */}
      </div>
      <Table className="w-full">
        <TableHeader className="bg-secondary p-4 text-[14px] w-full h-[50px] sm:h-[70px] font-bold">
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow
              key={headerGroup.id}
              className="!font-bold !text-secondary-foreground"
            >
              {headerGroup.headers.map((header) => {
                const isSortable = header.column.getCanSort();
                const sortState = getCurrentSortState(header.column.id);
                const isSorted = sortState !== "none";

                return (
                  <TableHead
                    key={header.id}
                    className={`font-bold text-[15px] relative 15px] p-1 sm:p-3
                    ${
                      header.column.columnDef.header === "#"
                        ? "hidden md:table-cell"
                        : ""
                    }  
                    ${
                      header.column.columnDef.header === "Name"
                        ? "sticky left-0 md:static bg-secondary md:bg-transparent z-10"
                        : ""
                    } ${
                      isSortable
                        ? "cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                        : ""
                    }`}
                    onClick={(e) => {
                      if (isSortable) {
                        handleColumnHeaderClick(e, header.column.id);
                      }
                    }}
                  >
                    <div className="flex items-center gap-1 text-[13px] sm:text-[15px] text-primary-foreground">
                      {!header.isPlaceholder &&
                        flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      {isSortable && (
                        <div
                          className={`transition-opacity ${
                            isSorted ? "opacity-100" : "opacity-30"
                          }`}
                        >
                          {sortState === "asc" ? (
                            <ArrowUp size={14} />
                          ) : sortState === "desc" ? (
                            <ArrowDown size={14} />
                          ) : (
                            <ArrowUp size={14} className="opacity-30" />
                          )}
                        </div>
                      )}
                    </div>
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>

        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row: any, index) => {
              // Determine the correct industry path for this specific row
              const rowIndustryPath =
                row.original?.industry?.slug || industryPath;

              return (
                <TableRow
                  key={row.id}
                  index={index}
                  data-state={row.getIsSelected() && "selected"}
                  className="h-[100px] cursor-pointer dark:border-slate-800"
                  onClick={(e) => {
                    // Only handle left-click navigation here
                    if (e.button === 0) {
                      console.log(
                        "Row clicked, using industry path:",
                        rowIndustryPath
                      );
                      window.location.href = `/${rowIndustryPath}/${
                        row.original?.slug || row.original?.documentId
                      }`;
                    }
                  }}
                >
                  {row.getVisibleCells().map((cell: any) => (
                    <TableCell
                      key={cell.id}
                      className={`p-1.5 sm:p-4 text-[12px] sm:text-sm
                        ${
                          cell.column.columnDef.accessorKey == "index"
                            ? "hidden md:table-cell"
                            : ""
                        }
                        ${
                          cell.column.columnDef.accessorKey == "name"
                            ? "sticky left-0 md:static bg-primary md:bg-transparent"
                            : ""
                        }`}
                    >
                      {cell.column.columnDef.accessorKey === "action" ? (
                        // For action cells, render content directly without anchor
                        flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )
                      ) : (
                        // For all other cells, keep the anchor wrapper
                        <a
                          href={`/${rowIndustryPath}/${
                            row.original?.slug || row.original?.documentId
                          }`}
                          className="contents"
                          onClick={(e) => {
                            // Just stop propagation to prevent double navigation
                            e.stopPropagation();
                          }}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </a>
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              );
            })
          ) : (
            <TableRow>
              <TableCell
                colSpan={defColumns.length}
                className="h-24 text-center text-xs sm:text-sm"
              >
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {isPagination && (
        <div className="border-t py-4 md:py-4">
          <DataTablePagination
            table={table}
            setCurrentPage={setCurrentPage}
            showSelectedCount={false}
          />
        </div>
      )}
    </div>
  );
}
