import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  X,
  <PERSON>ert<PERSON>ircle,
  Loader2,
  Link as LinkIcon,
  Search,
  Info,
} from "lucide-react";
import { useSelector, useDispatch } from "react-redux";
import _ from "lodash";
import {
  selectCategories,
  selectCategoryLoading,
  selectLoadingAffiliatePrograms,
  selectUserData,
} from "@/features/selectors";
import { categoryActions } from "@/features/rootActions";

// Constants for URL construction
const BASE_URL =
  process.env.NEXT_PUBLIC_AFFILIATE_BASE_URL || "https://affitor.com";
const STATIC_PATHS = [
  { value: "", label: "Homepage" },
  { value: "pricing", label: "Pricing" },
  { value: "category", label: "Category" },
  { value: "product", label: "Product" },
];
const URL_PARAM = "?via=";

export interface LinkFormData {
  name: string;
  path: string;
  viaValue: string;
  shortLink?: string; // New field for custom short link
  selectedCategory?: string; // For when path is "category"
  selectedProduct?: string; // For when path is "product"
  selectedPartner?: {
    id: string;
    documentId: string;
    userId: string;
    userDocumentId: string;
    name: string;
    email: string;
  }; // For admin mode
  // Complete URL will be constructed from the above
  url: string;
}

export interface LinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: LinkFormData) => void;
  initialData?: LinkFormData;
  title: string;
  isLoading?: boolean;
  isAdmin?: boolean; // New prop to enable admin mode
  apiError?: string | null; // New prop for API errors
  onClearError?: () => void; // New prop to clear API errors
  preselectedPartner?: {
    // New prop for pre-selected partner
    id: string;
    documentId: string;
    userId: string;
    userDocumentId: string;
    name: string;
    email: string;
  };
}

const LinkModal: React.FC<LinkModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  title,
  isLoading = false,
  isAdmin = false, // Default to false
  apiError = null, // Default to null
  onClearError,
  preselectedPartner,
}) => {
  const dispatch = useDispatch();
  const categories = useSelector(selectCategories);
  const isLoadingCategories = useSelector(selectCategoryLoading);
  const isLoadingAffiliates = useSelector(selectLoadingAffiliatePrograms);
  const userData = useSelector(selectUserData);

  // Admin partner selection state
  const [partnerSearchTerm, setPartnerSearchTerm] = useState("");
  const [partnerSearchInput, setPartnerSearchInput] = useState("");
  const [partnerSearchResult, setPartnerSearchResult] = useState<any[]>([]);
  const [isSearchingPartners, setIsSearchingPartners] = useState(false);
  const [isPartnerSearchDropdownOpen, setIsPartnerSearchDropdownOpen] =
    useState(false);

  // Product search state - applying SearchNav patterns (optimized)
  const [productSearchTerm, setProductSearchTerm] = useState("");
  const [productSearchInput, setProductSearchInput] = useState(""); // Separate input state for immediate UI updates
  const [productSearchResult, setProductSearchResult] = useState<
    Record<string, any[]>
  >({});
  const [isSearching, setIsSearching] = useState(false);
  const [selectedProductName, setSelectedProductName] = useState("");
  const [isSearchDropdownOpen, setIsSearchDropdownOpen] = useState(false);
  const [initialProducts, setInitialProducts] = useState<any[]>([]);
  const [isLoadingInitial, setIsLoadingInitial] = useState(false);

  // Create available paths (no longer need dynamic category paths since we have a dedicated category option)
  const availablePaths = STATIC_PATHS;

  // Initialize with default values
  const [formData, setFormData] = useState<LinkFormData>(
    initialData || {
      name: "",
      path: availablePaths[0]?.value || "",
      viaValue: "",
      shortLink: "",
      selectedCategory: "",
      selectedProduct: "",
      url: "",
    }
  );
  const [errors, setErrors] = useState<{
    name?: string;
    viaValue?: string;
    shortLink?: string;
  }>({});
  const [isTouched, setIsTouched] = useState<{
    name: boolean;
    viaValue: boolean;
    shortLink: boolean;
  }>({
    name: false,
    viaValue: false,
    shortLink: false,
  });

  // State to track if via parameter is auto-filled from user's referral code
  const [isViaAutoFilled, setIsViaAutoFilled] = useState(false);

  // Tooltip component for information icons
  const InfoTooltip: React.FC<{ content: string; children: React.ReactNode }> = ({ content, children }) => {
    const [isVisible, setIsVisible] = useState(false);
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
      const checkMobile = () => {
        setIsMobile(window.innerWidth < 768);
      };
      checkMobile();
      window.addEventListener('resize', checkMobile);
      return () => window.removeEventListener('resize', checkMobile);
    }, []);

    const handleClick = () => {
      if (isMobile) {
        setIsVisible(!isVisible);
      }
    };

    const handleMouseEnter = () => {
      if (!isMobile) {
        setIsVisible(true);
      }
    };

    const handleMouseLeave = () => {
      if (!isMobile) {
        setIsVisible(false);
      }
    };

    return (
      <div className="relative inline-block">
        <div
          onClick={handleClick}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          className="cursor-help"
        >
          {children}
        </div>
        {isVisible && (
          <div className="absolute z-50 w-64 p-2 text-xs text-white bg-gray-900 rounded-lg shadow-lg -top-2 left-6 transform -translate-y-full">
            <div className="relative">
              {content}
              <div className="absolute top-full left-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Load categories if needed
  useEffect(() => {
    if (categories.length <= 1) {
      // Only "All Programs" exists
      dispatch(categoryActions.fetchAll());
    }
  }, [dispatch, categories.length]);



  // Effect to set preselected partner if provided
  useEffect(() => {
    if (preselectedPartner && isAdmin) {
      setFormData((prev) => ({
        ...prev,
        selectedPartner: preselectedPartner,
      }));
      setPartnerSearchInput(preselectedPartner.name);
      setPartnerSearchTerm(preselectedPartner.name);
    }
  }, [preselectedPartner, isAdmin]);

  // Effect to auto-fill via parameter from user's referral code
  useEffect(() => {
    if (userData?.referrer?.referral_code && !initialData?.viaValue) {
      // User has a referral code and no initial via value is provided
      setFormData((prev) => ({
        ...prev,
        viaValue: userData.referrer.referral_code,
      }));
      setIsViaAutoFilled(true);
    }
  }, [userData, initialData]);

  const handlePartnerSearch = async (searchTerm: string) => {
    if (!searchTerm || searchTerm.trim().length < 2) {
      setPartnerSearchResult([]);
      return;
    }

    try {
      setIsSearchingPartners(true);
      setPartnerSearchTerm(searchTerm);

      // Get admin token from localStorage
      const token = localStorage.getItem("admin_token");
      if (!token) {
        console.error("Admin authentication required");
        return;
      }

      const response = await fetch(
        `/api/admin/partners?search=${encodeURIComponent(
          searchTerm
        )}&pageSize=10`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to search partners");
      }

      const data = await response.json();

      // Transform the data to match our expected format
      const partners =
        data.results?.map((partner: any) => ({
          id: partner.id,
          documentId: partner.documentId,
          userId: partner.user?.id?.toString(),
          userDocumentId: partner.user?.documentId,
          name:
            `${partner.user?.first_name || ""} ${
              partner.user?.last_name || ""
            }`.trim() || partner.user?.email,
          email: partner.user?.email,
        })) || [];

      setPartnerSearchResult(partners);
      setIsPartnerSearchDropdownOpen(true);
    } catch (error) {
      console.error("Error searching partners:", error);
      setPartnerSearchResult([]);
    } finally {
      setIsSearchingPartners(false);
    }
  };

  const handleProductSearch = (text: string) => {
    setProductSearchTerm(text);

    // Only search if we have at least 2 characters to reduce unnecessary API calls
    if (text.trim().length < 2) {
      return;
    }
  };

  const fetchProductSearch = useCallback(async (searchTerm: string) => {
    try {
      setIsSearching(true);
      const response = await fetch(
        `/api/affiliates?filters[name][$containsi]=${searchTerm}&populate[image][fields][0]=id&populate[image][fields][1]=url&populate[industry][fields][0]=slug`
      );
      const json = await response.json();
      const { data } = json;

      if (!data) return;
      if (data.statusCode) {
        console.error("Error fetching affiliate programs:", data.message);
        return;
      }

      // Cache results by search term (following SearchNav pattern)
      setProductSearchResult((prev) => ({
        ...prev,
        [searchTerm]: data.map((program: any) => ({
          name: program.name,
          id: program.documentId,
          image: program.image,
          industry: program.industry,
          slug: program.slug || program.documentId,
          company_name: program.company_name,
          url: program.url,
        })),
      }));
    } catch (error) {
      console.error("Error fetching affiliate programs:", error);
    } finally {
      setIsSearching(false);
    }
  }, []);

  const fetchInitialProducts = useCallback(async () => {
    try {
      setIsLoadingInitial(true);
      const response = await fetch(
        `/api/affiliates?pagination[page]=1&pagination[pageSize]=100&populate[image][fields][0]=id&populate[image][fields][1]=url&populate[industry][fields][0]=slug&sort[0]=name:asc`
      );
      const json = await response.json();
      const { data } = json;

      if (!data) return;
      if (data.statusCode) {
        console.error("Error fetching initial products:", data.message);
        return;
      }

      setInitialProducts(
        data.map((program: any) => ({
          name: program.name,
          id: program.documentId,
          image: program.image,
          industry: program.industry,
          slug: program.slug || program.documentId,
          company_name: program.company_name,
          url: program.url,
        }))
      );
    } catch (error) {
      console.error("Error fetching initial products:", error);
    } finally {
      setIsLoadingInitial(false);
    }
  }, []);

  // Use useMemo for efficient product management (optimized - no side effects)
  const filteredProducts = useMemo(() => {
    if (!productSearchTerm || productSearchTerm.trim().length < 2) return [];
    return productSearchResult[productSearchTerm] || [];
  }, [productSearchTerm, productSearchResult]);

  // Effect to trigger search when productSearchTerm changes (separated from useMemo)
  useEffect(() => {
    if (productSearchTerm && productSearchTerm.trim().length >= 2) {
      // Only fetch if not already cached
      if (!productSearchResult[productSearchTerm]) {
        fetchProductSearch(productSearchTerm);
      }
    }
  }, [productSearchTerm, productSearchResult, fetchProductSearch]);

  // Optimized debounced search (separated input state from search state)
  const debounceProductSearch = useCallback(
    _.debounce((text: string) => {
      handleProductSearch(text);
    }, 500), // Reduced back to 500ms since we fixed the main issues
    []
  );

  // Effect to manage dropdown visibility (following SearchNav pattern)
  useEffect(() => {
    if (productSearchTerm && productSearchTerm.trim().length >= 2) {
      // Small delay before showing dropdown to prevent flickering
      const timer = setTimeout(() => {
        setIsSearchDropdownOpen(true);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setIsSearchDropdownOpen(false);
    }
  }, [productSearchTerm]);

  // Construct the full URL whenever path, selectedCategory, selectedProduct, or viaValue changes
  useEffect(() => {
    let constructedUrl = "";

    if (formData.path === "category" && formData.selectedCategory) {
      // Category path format: https://affitor.com/category-slug?via=yourname
      constructedUrl = `${BASE_URL}/${formData.selectedCategory}${URL_PARAM}${formData.viaValue}`;
    } else if (formData.path === "product" && formData.selectedProduct) {
      // Product path format: https://affitor.com/product/product-slug?via=yourname
      constructedUrl = `${BASE_URL}/product/${formData.selectedProduct}${URL_PARAM}${formData.viaValue}`;
    } else if (
      formData.path &&
      formData.path !== "category" &&
      formData.path !== "product"
    ) {
      // Regular path format: https://affitor.com/pricing?via=yourname
      constructedUrl = `${BASE_URL}/${formData.path}${URL_PARAM}${formData.viaValue}`;
    } else {
      // Homepage format: https://affitor.com/?via=yourname
      constructedUrl = `${BASE_URL}/${URL_PARAM}${formData.viaValue}`;
    }

    setFormData((prev) => ({ ...prev, url: constructedUrl }));
  }, [
    formData.path,
    formData.selectedCategory,
    formData.selectedProduct,
    formData.viaValue,
  ]);

  // Track previous open state to detect when modal is first opened
  const [prevIsOpen, setPrevIsOpen] = useState(false);

  // Effect to clear API error when modal is first opened (not when already open)
  useEffect(() => {
    if (isOpen && !prevIsOpen && onClearError) {
      // Clear error only when modal transitions from closed to open
      onClearError();
    }
    setPrevIsOpen(isOpen);
  }, [isOpen, prevIsOpen, onClearError]);

  // Effect to prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      // Save current scroll position
      const scrollY = window.scrollY;

      // Prevent body scroll
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = "100%";

      return () => {
        // Restore body scroll
        document.body.style.overflow = "";
        document.body.style.position = "";
        document.body.style.top = "";
        document.body.style.width = "";

        // Restore scroll position
        window.scrollTo(0, scrollY);
      };
    }
  }, [isOpen]);

  // Effect to handle scroll fade indicators
  useEffect(() => {
    if (!isOpen) return;

    const handleScroll = (e: Event) => {
      const target = e.target as HTMLElement;
      const scrollTop = target.scrollTop;
      const scrollHeight = target.scrollHeight;
      const clientHeight = target.clientHeight;

      const topFade = document.getElementById('scroll-top-fade');
      const bottomFade = document.getElementById('scroll-bottom-fade');

      if (topFade) {
        topFade.style.opacity = scrollTop > 10 ? '1' : '0';
      }

      if (bottomFade) {
        bottomFade.style.opacity = scrollTop < scrollHeight - clientHeight - 10 ? '1' : '0';
      }
    };

    const scrollContainer = document.querySelector('.modal-content');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      // Initial check
      handleScroll({ target: scrollContainer } as unknown as Event);

      return () => {
        scrollContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [isOpen]);

  // Effect to load initial products when product path is selected
  useEffect(() => {
    if (
      formData.path === "product" &&
      initialProducts.length === 0 &&
      !productSearchTerm
    ) {
      fetchInitialProducts();
    }
  }, [formData.path, initialProducts.length, productSearchTerm]);

  // Reset form data when initialData changes
  useEffect(() => {
    if (initialData) {
      // If initialData contains a full URL, parse it to extract path and viaValue
      if (initialData.url) {
        try {
          const url = new URL(initialData.url);
          let path = "";
          let viaValue = "";
          let selectedCategory = "";
          let selectedProduct = "";

          // Check if it's a category URL (now uses URL path instead of hash)
          if (
            url.pathname &&
            url.pathname !== "/" &&
            !url.pathname.startsWith("/product/")
          ) {
            // Category URL format: /category-slug
            path = "category";
            selectedCategory = url.pathname.replace(/^\//, ""); // Remove leading slash
            viaValue = url.searchParams.get("via") || "";
          } else if (url.pathname.startsWith("/product/")) {
            // Product URL format: /product/product-slug
            path = "product";
            selectedProduct = url.pathname.replace("/product/", "");
            viaValue = url.searchParams.get("via") || "";
          } else {
            // Regular path
            path = url.pathname.replace(/^\//, ""); // Remove leading slash
            viaValue = url.searchParams.get("via") || "";
          }

          setFormData({
            name: initialData.name,
            path: path,
            viaValue: viaValue,
            shortLink: initialData.shortLink || "",
            selectedCategory: selectedCategory,
            selectedProduct: selectedProduct,
            url: initialData.url,
          });

          // Set selected product name for display (use the slug for now)
          if (selectedProduct) {
            setSelectedProductName(selectedProduct);
            setProductSearchTerm(selectedProduct);
            setProductSearchInput(selectedProduct);
          }
        } catch {
          setFormData(initialData);
        }
      } else {
        setFormData(initialData);
      }
      setErrors({});
      setIsTouched({ name: false, viaValue: false, shortLink: false });
    }
  }, [initialData]);

  // Validate via value when it changes
  useEffect(() => {
    if (isTouched.viaValue) {
      validateViaValue(formData.viaValue);
    }
  }, [formData.viaValue, isTouched.viaValue]);

  // Validate short link when it changes
  useEffect(() => {
    if (isTouched.shortLink) {
      validateShortLink(formData.shortLink || "");
    }
  }, [formData.shortLink, isTouched.shortLink, categories]);

  const validateViaValue = (value: string): boolean => {
    if (!value.trim()) {
      setErrors((prev) => ({ ...prev, viaValue: "Via value is required" }));
      return false;
    }

    // Only allow alphanumeric characters and hyphens for via value
    if (!/^[a-zA-Z0-9-]+$/.test(value)) {
      setErrors((prev) => ({
        ...prev,
        viaValue: "Only letters, numbers, and hyphens are allowed",
      }));
      return false;
    }

    setErrors((prev) => ({ ...prev, viaValue: undefined }));
    return true;
  };

  const validateShortLink = (value: string): boolean => {
    // Short link is optional, so empty value is valid
    if (!value.trim()) {
      setErrors((prev) => ({ ...prev, shortLink: undefined }));
      return true;
    }

    // Only allow alphanumeric characters and hyphens for short link
    if (!/^[a-zA-Z0-9-]+$/.test(value)) {
      setErrors((prev) => ({
        ...prev,
        shortLink: "Only letters, numbers, and hyphens are allowed",
      }));
      return false;
    }

    // Check if short link conflicts with category slugs
    const conflictsWithCategory = categories.some(
      (category) =>
        category.slug && category.slug.toLowerCase() === value.toLowerCase()
    );

    if (conflictsWithCategory) {
      setErrors((prev) => ({
        ...prev,
        shortLink:
          "This short link conflicts with an existing category. Please choose a different one.",
      }));
      return false;
    }

    setErrors((prev) => ({ ...prev, shortLink: undefined }));
    return true;
  };

  const validateForm = (): boolean => {
    const viaValueValid = validateViaValue(formData.viaValue);
    const shortLinkValid = validateShortLink(formData.shortLink || "");

    // Validate name
    let nameValid = true;
    if (!formData.name.trim()) {
      setErrors((prev) => ({ ...prev, name: "Name is required" }));
      nameValid = false;
    } else {
      setErrors((prev) => ({ ...prev, name: undefined }));
    }

    return viaValueValid && nameValid && shortLinkValid;
  };

  const handleSubmit = () => {
    // Mark all fields as touched
    setIsTouched({ name: true, viaValue: true, shortLink: true });

    // Validate the entire form
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md max-h-[90vh] sm:max-h-[85vh] overflow-hidden transform transition-all flex flex-col">
        {/* Header - Fixed */}
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center flex-shrink-0">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {title}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
            disabled={isLoading}
            aria-label="Close modal"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-y-auto min-h-0 scrollbar-thin modal-content relative"
             style={{ WebkitOverflowScrolling: 'touch' }}>
          {/* API Error Display */}
          {apiError && (
            <div className="mx-4 sm:mx-6 mt-4 mb-2 p-3 sm:p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-start">
                <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-red-800 dark:text-red-300 mb-1">
                    {initialData ? "Error Updating Link" : "Error Creating Link"}
                  </h4>
                  <p className="text-sm text-red-700 dark:text-red-400">
                    {apiError}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Body */}
          <div className="p-4 sm:p-6 pb-6">
            <div className="space-y-5">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                Link Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                onBlur={() => setIsTouched((prev) => ({ ...prev, name: true }))}
                className={`w-full p-2.5 border ${
                  errors.name
                    ? "border-red-500 dark:border-red-500"
                    : "border-gray-300 dark:border-gray-600"
                } rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all`}
                placeholder="e.g. Homepage Link"
                disabled={isLoading}
              />
              {errors.name && isTouched.name && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-3.5 h-3.5 mr-1" />
                  {errors.name}
                </p>
              )}
            </div>

            <div>
              <div className="flex items-center mb-1.5">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Short Link (Optional)
                </label>
                <InfoTooltip content="Create a custom short identifier that will redirect to your URL. For example, 'my-special-link' will create affitor.com/my-special-link that redirects to your destination URL.">
                  <Info className="w-4 h-4 ml-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                </InfoTooltip>
              </div>
              <div className="flex items-center">
                <div className="bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-300 px-3 py-2.5 rounded-l-lg border border-r-0 border-gray-300 dark:border-gray-600 whitespace-nowrap flex-shrink-0">
                  {BASE_URL}/
                </div>
                <input
                  type="text"
                  value={formData.shortLink || ""}
                  onChange={(e) =>
                    setFormData({ ...formData, shortLink: e.target.value })
                  }
                  onBlur={() =>
                    setIsTouched((prev) => ({ ...prev, shortLink: true }))
                  }
                  className={`w-full p-2.5 border rounded-r-lg ${
                    errors.shortLink
                      ? "border-red-500 dark:border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all`}
                  placeholder="my-ai"
                  disabled={isLoading}
                />
              </div>
              {errors.shortLink && isTouched.shortLink && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-3.5 h-3.5 mr-1" />
                  {errors.shortLink}
                </p>
              )}
            </div>



            {/* Admin partner selection - Show only in admin mode and if no preselected partner */}
            {isAdmin && !preselectedPartner && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                  Select Partner
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={partnerSearchInput}
                    onChange={(e) => {
                      setPartnerSearchInput(e.target.value);
                      // Debounce the search to avoid excessive API calls
                      setTimeout(() => {
                        if (e.target.value.trim().length >= 2) {
                          handlePartnerSearch(e.target.value);
                        } else {
                          setPartnerSearchResult([]);
                        }
                      }, 300);
                    }}
                    onFocus={() => {
                      if (partnerSearchInput || partnerSearchTerm)
                        setIsPartnerSearchDropdownOpen(true);
                    }}
                    onBlur={() => {
                      setTimeout(() => {
                        setIsPartnerSearchDropdownOpen(false);
                      }, 300);
                    }}
                    placeholder="Search for partners..."
                    className="w-full pl-10 p-2.5 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                    disabled={isLoading}
                  />
                  {isSearchingPartners && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                    </div>
                  )}
                </div>

                {/* Partner Results */}
                {partnerSearchTerm &&
                  isPartnerSearchDropdownOpen &&
                  partnerSearchResult.length > 0 && (
                    <div className="mt-2 max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700">
                      {partnerSearchResult.map((partner) => (
                        <div
                          key={partner.id}
                          onClick={() => {
                            setFormData({
                              ...formData,
                              selectedPartner: partner,
                            });
                            setPartnerSearchTerm(partner.name);
                            setPartnerSearchInput(partner.name);
                            setIsPartnerSearchDropdownOpen(false);
                          }}
                          className={`p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 border-b border-gray-200 dark:border-gray-600 last:border-b-0 ${
                            formData.selectedPartner?.id === partner.id
                              ? "bg-blue-50 dark:bg-blue-900/20"
                              : ""
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {partner.name}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                {partner.email}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                {/* Search states */}
                {(!partnerSearchInput ||
                  partnerSearchInput.trim().length < 2) &&
                  !formData.selectedPartner && (
                    <div className="mt-2 p-3 text-sm text-gray-500 dark:text-gray-400 text-center border border-gray-300 dark:border-gray-600 rounded-lg">
                      Type at least 2 characters to search for partners...
                    </div>
                  )}

                {/* Selected partner display */}
                {formData.selectedPartner && (
                  <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div className="text-xs text-blue-600 dark:text-blue-400">
                      Selected Partner:
                    </div>
                    <div className="text-sm font-medium text-blue-900 dark:text-blue-300">
                      {formData.selectedPartner.name}
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        setFormData({
                          ...formData,
                          selectedPartner: undefined,
                        });
                        setPartnerSearchTerm("");
                        setPartnerSearchInput("");
                        setIsPartnerSearchDropdownOpen(false);
                      }}
                      className="mt-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                    >
                      Clear selection
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Show preselected partner info in admin mode */}
            {isAdmin && preselectedPartner && (
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="text-xs text-blue-600 dark:text-blue-400">
                  Creating link for:
                </div>
                <div className="text-sm font-medium text-blue-900 dark:text-blue-300">
                  {preselectedPartner.name}
                </div>
                <div className="text-xs text-blue-600 dark:text-blue-400">
                  {preselectedPartner.email}
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                Page Path
              </label>
              <select
                value={formData.path}
                onChange={(e) => {
                  setFormData({
                    ...formData,
                    path: e.target.value,
                    selectedCategory: "",
                    selectedProduct: "",
                  });
                  setSelectedProductName("");
                  setProductSearchTerm("");
                  setProductSearchInput("");
                  setIsSearchDropdownOpen(false);
                  // Reset initial products when path changes
                  if (e.target.value !== "product") {
                    setInitialProducts([]);
                  }
                }}
                className="w-full p-2.5 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                disabled={
                  isLoading || isLoadingCategories || isLoadingAffiliates
                }
              >
                {availablePaths.map((path) => (
                  <option key={path.value} value={path.value}>
                    {path.label}
                  </option>
                ))}
                {isLoadingCategories && (
                  <option disabled>Loading categories...</option>
                )}
                {isLoadingAffiliates && (
                  <option disabled>Loading products...</option>
                )}
              </select>
            </div>

            {/* Category Selection - Show only when path is "category" */}
            {formData.path === "category" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                  Select Category
                </label>
                <select
                  value={formData.selectedCategory}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      selectedCategory: e.target.value,
                    })
                  }
                  className="w-full p-2.5 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                  disabled={isLoading || isLoadingCategories}
                >
                  <option value="">Select a category...</option>
                  {categories
                    .filter((cat) => cat.slug) // Only include categories with slugs (exclude "All Programs")
                    .map((category) => (
                      <option key={category.id} value={category.slug}>
                        {category.name}
                      </option>
                    ))}
                  {isLoadingCategories && (
                    <option disabled>Loading categories...</option>
                  )}
                </select>
              </div>
            )}

            {/* Product Selection - Show only when path is "product" */}
            {formData.path === "product" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                  Search for Product
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={productSearchInput}
                    onChange={(e) => {
                      setProductSearchInput(e.target.value);
                      debounceProductSearch(e.target.value);
                    }}
                    onFocus={() => {
                      if (productSearchInput || productSearchTerm)
                        setIsSearchDropdownOpen(true);
                    }}
                    onBlur={() => {
                      setTimeout(() => {
                        setIsSearchDropdownOpen(false);
                      }, 300);
                    }}
                    placeholder="Search for specific affiliate programs..."
                    className="w-full pl-10 p-2.5 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                    disabled={isLoading}
                  />
                  {isSearching && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                    </div>
                  )}
                </div>

                {/* Product Results - Show search results or initial products */}
                {((productSearchTerm &&
                  productSearchTerm.trim().length >= 2 &&
                  filteredProducts &&
                  filteredProducts.length > 0 &&
                  isSearchDropdownOpen) ||
                  (!productSearchTerm && initialProducts.length > 0)) && (
                  <div className="mt-2 max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700">
                    {(!productSearchTerm
                      ? initialProducts
                      : filteredProducts.slice(0, 10)
                    ).map((product) => (
                      <div
                        key={product.id}
                        onClick={() => {
                          setFormData({
                            ...formData,
                            selectedProduct: product.slug || product.id,
                          });
                          setSelectedProductName(product.name);
                          setProductSearchTerm(product.name);
                          setProductSearchInput(product.name);
                          setIsSearchDropdownOpen(false);
                        }}
                        className={`p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 border-b border-gray-200 dark:border-gray-600 last:border-b-0 ${
                          formData.selectedProduct ===
                          (product.slug || product.id)
                            ? "bg-blue-50 dark:bg-blue-900/20"
                            : ""
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          {product.image?.url && (
                            <img
                              src={product.image.url}
                              alt={product.name}
                              className="w-8 h-8 rounded object-cover flex-shrink-0"
                            />
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {product.name}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                              {product.url || product.company_name}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Selected product display */}
                {formData.selectedProduct && (
                  <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div className="text-xs text-blue-600 dark:text-blue-400">
                      Selected Product:
                    </div>
                    <div className="text-sm font-medium text-blue-900 dark:text-blue-300">
                      {selectedProductName || formData.selectedProduct}
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        setFormData({
                          ...formData,
                          selectedProduct: "",
                        });
                        setSelectedProductName("");
                        setProductSearchTerm("");
                        setProductSearchInput("");
                        setIsSearchDropdownOpen(false);
                      }}
                      className="mt-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                    >
                      Clear selection
                    </button>
                  </div>
                )}
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                Via Parameter Value
              </label>
              <div className="flex items-center">
                <div className="bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-300 px-5 py-2.5 rounded-l-lg border border-r-0 border-gray-300 dark:border-gray-600 whitespace-nowrap flex-shrink-0">
                  {URL_PARAM}
                </div>
                <input
                  type="text"
                  value={formData.viaValue}
                  onChange={(e) =>
                    setFormData({ ...formData, viaValue: e.target.value })
                  }
                  onBlur={() =>
                    setIsTouched((prev) => ({ ...prev, viaValue: true }))
                  }
                  className={`w-full p-2.5 border rounded-r-lg ${
                    errors.viaValue
                      ? "border-red-500 dark:border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  } ${
                    isViaAutoFilled
                      ? "bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                      : "bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  } focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all`}
                  placeholder="yourname"
                  disabled={isLoading || isViaAutoFilled}
                  title={
                    isViaAutoFilled
                      ? "Via parameter is auto-filled from your referral code and cannot be changed"
                      : ""
                  }
                />
              </div>
              {errors.viaValue && isTouched.viaValue && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-3.5 h-3.5 mr-1" />
                  {errors.viaValue}
                </p>
              )}
              {isViaAutoFilled && (
                <p className="mt-1 text-sm text-blue-600 dark:text-blue-400 flex items-center">
                  <AlertCircle className="w-3.5 h-3.5 mr-1" />
                  Via parameter is auto-filled from your referral code and
                  cannot be changed after first input.
                </p>
              )}
            </div>

            {/* URL Preview */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                URL Preview
              </div>
              <div className="flex items-center text-blue-600 dark:text-blue-400 break-all font-mono text-sm">
                <LinkIcon className="w-4 h-4 mr-1.5 flex-shrink-0" />
                {formData.url}
              </div>
            </div>
            </div>
          </div>

          {/* Scroll fade indicators */}
          <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-white dark:from-gray-800 to-transparent pointer-events-none opacity-0 transition-opacity duration-200" id="scroll-top-fade"></div>
          <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-white dark:from-gray-800 to-transparent pointer-events-none opacity-0 transition-opacity duration-200" id="scroll-bottom-fade"></div>
        </div>

        {/* Footer - Fixed */}
        <div className="px-4 sm:px-6 py-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3 flex-shrink-0">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 transition-colors"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center min-w-[80px] transition-colors shadow-sm"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              "Save"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LinkModal;
