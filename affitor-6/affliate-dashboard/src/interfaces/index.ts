export type AppError = {
  statusCode: number;
  message: string;
};

export type TSocialSearchResults = {
  result: {
    tiktokResults: Array<TPost>;
    youtubeResults: Array<TPost>;
  };
};

export type TPost = {
  id: string;
  title: string;
  description: string;
  channelTitle: string;
  publishedAt: string;
  thumbnail: string;
  views: number;
  likes: number;
  comments: number;
  link: string;
};

export type TStatistics = {
  total_views: number;
  total_likes: number;
  total_comments: number;
  most_viewed: TPost;
  most_liked: TPost;
  most_commented: TPost;
};

export type TColumns = {
  id: string;
  label: string;
  enabled: boolean;
  sortable?: boolean;
};

export interface ICategory {
  id: string;
  documentId: string;
  name: string;
  slug: string;
}

export interface ITag {
  id: string;
  icon: {
    url: string;
  } | null;
  name: string;
}

export interface IAffiliate {
  documentId: string;
  name: string;
  detail: {
    children: {
      text: string;
    }[];
  }[];
  categories: ICategory[];
  tags: ITag[];
  established: number;
  monthly_traffic: number;
  commission_detail: string;
  commission: ICommission;
  link: string;
  image: {
    url: string;
  };
  currency: string;
  tag_line: string;
  company_name: string;
  traffic_rank: number;
  cookies_duration: string;
  payment_methods: IPaymentMethod[];
  minimum_payout: number;
  launch_year: number;
  contact_information: string;
  trust_score: number;
  traffic_data: any[];
  affiliate_url: string;
  features: string;
  country: string;
  url: string;
  avg_conversion: number;
  recurring: string;
  pricing: string;
  industry: any;
  slug: string;
}

export interface ICommission {
  title: string;
  note: string;
  value_from: number;
  value_to: number;
  commission_detail: any;
}

export interface ICommissionLevel {
  description: string;
  value: number;
}

export interface IPaymentMethod {
  name: string;
  image: {
    url: string;
  };
}

export interface ISocial {
  documentId: string;
  title: string;
  description: string;
  published_from: string;
  channel_title: string;
  channel_avatar?: string;
  thumbnail: string;
  channel_id: string;
  likes?: number;
  comments?: number;
  link: string;
  views: number;
  type: string;
  platform: string;
}

export interface IXPost extends ISocial {
  photos: string[];
  x_id: string;
  retweets: number;
}

export interface IRedditPost extends ISocial {
  post_id: string;
}
export interface IVideo extends ISocial {
  video_id: string;
  duration: string;
  video_link: string;
  transcript: string | null;
}

export interface ISocialListening {
  id: string;
  title: string;
  views: number;
  comments: number;
  likes: number;
  shares: number;
  duration: string;
  description: string;
}

export interface IPagination {
  page: number;
  pageSize?: number;
  pageCount?: number;
  total?: number;
}

export interface IMeta {
  pagination: IPagination;
}

export interface ISort {
  field: string;
  order: "asc" | "desc";
}
export interface ITrafficWeb {
  documentId: string;
  bounce_rate: number;
  page_per_visit: number;
  visits: number;
  time_on_site: number;
  month: number;
  year: number;
  chart: {
    [date: string]: number;
  };
  top_keyword: ITopKeyword;
  top_countries: {
    country: string;
    country_code: string;
    url_code: string;
    country_name: string;
    value: number;
    visits: number;
    percentage: string;
  }[];
  global_rank: number;
  country_rank: {
    rank: number;
    country_code: string;
    country: string;
    country_name: string;
  };
  category_rank: {
    rank: number;
    category: string;
  };
  traffic_sources: ITrafficSources;
}

// Define interface for traffic sources
export interface ITrafficSources {
  mail: number;
  direct: number;
  search: number;
  social: number;
  referrals: number;
  paid_referrals: number;
}

export interface ITopKeyword {
  summary: {
    total_keywords: number;
    traffic_origin_search: number;
    avg_cpc: number;
  };
  keywords: {
    rank: number;
    name: string;
    traffic: number;
    cpc: number;
  }[];
}

export interface IPaymentMethodDetail {
  id: number;
  documentId: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}

export interface PaymentMethodsResponse {
  data: IPaymentMethodDetail[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

interface FilterRange {
  from: string;
  to: string;
}

export interface FilterState {
  pricing: { from: string; to: string };
  commission: { from: string; to: string };
  conversion: { from: string; to: string };
  monthlyTraffic: { from: string; to: string };
  cookiesDuration: { from: string; to: string };
  category: string;
  paymentMethod: string;
  recurring: string;
  countries: string[];
  launchYears: string[];
}

export interface ISubscriptionTier {
  id: number;
  documentId: string;
  name: string;
  display_name: string;
  price: number;
  request_limit: number;
  duration_days: number;
  features: string[] | null;
  is_popular: boolean;
  description: string | null;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  tier_features: {
    item: string;
  }[];
  stripe_recurring_interval: "month" | "quarter" | "year";
}

export interface IAd {
  id: number;
  documentId: string;
  ad_id?: string;
  ad_title?: string;
  title?: string;
  platform: string;
  thumbnail?: string;
  video_info?: {
    vid?: string;
    cover?: string;
    width?: number;
    height?: number;
    duration?: number;
    video_url?: {
      [resolution: string]: string;
    };
  };
  brand_name?: string;
  ctr?: number;
  cost?: number;
  industry_key?: string;
  country_code?: string[];
  landing_page?: string;
  like?: number;
  comment?: number;
  share?: number;
  objective_key?: string;
  objectives?: {
    label: string;
    value: number;
  }[];
  source?: string;
  is_search?: boolean;
  favorite?: boolean;
  keyword?: string;
  last_fetched?: string;
  is_displayed?: boolean;
  views: number;
  clicks?: number;
  avgCostPerDay?: number;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
  seen_in?: string[];
  affiliate?: {
    id: string;
    name: string;
    slug?: string;
    industry?: {
      slug: string;
    };
    image?: {
      url: string;
    };
    categories?: {
      id: string;
      name: string;
      slug: string;
    }[];
  };
}

export interface ITransaction {
  id: number;
  documentId: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  amount: number;
  currency: string;
  payment_status: "pending" | "completed" | "failed" | "cancelled";
  payment_method: "stripe" | "paypal" | "bank_transfer" | "credit_card";
  transaction_date: string;
  payment_details: any;
  stripe_checkout_session?: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  stripe_price_id?: string;
  stripe_invoice_id?: string;
  current_period_start?: string;
  current_period_end?: string;
  auto_renew?: boolean;
  cancellation_date?: string;
  cancellation_reason?: string;
  is_checked_subscription?: boolean;
  user: {
    id: number;
    documentId: string;
    username: string;
    email: string;
    first_name?: string;
    last_name?: string;
    referral?: {
      id: number;
      documentId: string;
      referral_status: string;
      total_paid: number;
      referrer?: {
        id: number;
        documentId: string;
        referral_code: string;
        referrer_status: string;
        balance?: number;
        total_earnings?: number;
      };
    };
  };
  subscription_tier?: any;
  child_transactions?: {
    count: number;
  };
  parent_transaction?: any;
  status: string;
}
