import { call, put, takeLatest, select, take } from "redux-saga/effects";
import { actions } from "./referrer-links.slice";
import { actions as userActions } from "../user/user.slice";
import { PayloadAction } from "@reduxjs/toolkit";

function* handleFetchLinks(
  action: PayloadAction<{ page?: number; pageSize?: number }>
): Generator<any, void, any> {
  try {
    // Get auth token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      yield put(actions.setError("Authentication required"));
      return;
    }

    // Get pagination parameters or use defaults
    const { page = 1, pageSize = 10 } = action.payload || {};

    // Build query string in the format expected by Strapi (nested pagination object)
    const queryParams = new URLSearchParams({
      "pagination[page]": page.toString(),
      "pagination[pageSize]": pageSize.toString(),
    });

    // Make request to API route instead of direct Strapi client
    const response = yield call(fetch, `/api/referrer-links?${queryParams}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = yield response.json();

    // Extract data and meta from API response
    const { data, meta } = result;

    // Set links and pagination data
    yield put(actions.fetchLinksSuccess(data));

    // Set pagination if meta is provided by the API
    if (meta?.pagination) {
      yield put(actions.setPagination(meta.pagination));
    } else {
      // Fallback pagination in case API doesn't provide it
      yield put(
        actions.setPagination({
          page,
          pageSize,
          pageCount: Math.ceil(data.length / pageSize),
          total: data.length,
        })
      );
    }
  } catch (error: any) {
    console.error("Error fetching referrer links:", error);
    yield put(
      actions.fetchLinksFailure(
        error.message || "Failed to fetch referrer links"
      )
    );
  }
}

function* handleCreateLink(
  action: PayloadAction<{ name: string; url: string; shortLink?: string }>
): Generator<any, void, any> {
  try {
    // Get auth token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      yield put(actions.setError("Authentication required"));
      return;
    }

    // Get current user data from store
    const userData = yield select((state) => state.user.data);

    // Extract via parameter from the URL
    let viaValue = "";
    try {
      const urlObj = new URL(action.payload.url);
      viaValue = urlObj.searchParams.get("via") || "";
    } catch (error) {
      console.error("Error parsing URL:", error);
    }

    // Check if user doesn't have a referrer_code but is creating a link with via parameter
    if (!userData?.referrer?.referral_code && viaValue) {
      console.log(
        "User doesn't have referrer_code, updating profile with code:",
        viaValue
      );

      // First, update user profile with the referrer_code
      yield put(userActions.updateUserProfile({ referrer_code: viaValue }));

      // Wait for the profile update to complete
      yield take(userActions.updateUserProfileSuccess.type);

      // Refresh user data to get the updated referrer information and bind to UI
      yield put(userActions.fetchUserMe());

      // Wait for the user data refresh to complete to ensure UI binding
      yield take(userActions.setUserData.type);

      console.log(
        "User profile updated and data refreshed, proceeding with link creation"
      );
    }

    // Prepare the request payload
    const requestPayload = action.payload;

    // Make request to API route instead of direct Strapi client
    const response = yield call(fetch, "/api/referrer-links", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestPayload),
    });

    if (!response.ok) {
      // Try to extract error message from response
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorData = yield response.json();
        if (errorData.message) {
          errorMessage = errorData.message;
        }
      } catch (e) {
        // If we can't parse the error response, use the default message
      }
      throw new Error(errorMessage);
    }

    const data = yield response.json();
    yield put(actions.createLinkSuccess(data));

    // Fetch the updated list after successful creation
    yield put(actions.fetchLinks({ page: 1, pageSize: 25 }));
  } catch (error: any) {
    console.error("Error creating referrer link:", error);
    yield put(
      actions.setError(error.message || "Failed to create referrer link")
    );
  }
}

function* handleUpdateLink(
  action: PayloadAction<{
    id: string;
    documentId: string;
    name: string;
    url: string;
    shortLink?: string;
  }>
): Generator<any, void, any> {
  try {
    // Get auth token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      yield put(actions.setError("Authentication required"));
      return;
    }

    const { documentId, name, url, shortLink } = action.payload;

    // Make request to API route instead of direct Strapi client
    const response = yield call(fetch, `/api/referrer-links/${documentId}`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ name, url, shortLink }),
    });

    if (!response.ok) {
      // Try to extract error message from response
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorData = yield response.json();
        if (errorData.message) {
          errorMessage = errorData.message;
        }
      } catch (e) {
        // If we can't parse the error response, use the default message
      }
      throw new Error(errorMessage);
    }

    // Fetch the updated list after successful update to ensure UI is in sync
    yield put(actions.fetchLinks({ page: 1, pageSize: 25 }));

    // Set loading to false since the operation completed successfully
    yield put(actions.updateLinkSuccess(null));
  } catch (error: any) {
    console.error("Error updating referrer link:", error);
    yield put(
      actions.setError(error.message || "Failed to update referrer link")
    );
  }
}

function* handleDeleteLink(
  action: PayloadAction<{ id: string; documentId: string }>
): Generator<any, void, any> {
  try {
    // Get auth token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      yield put(actions.setError("Authentication required"));
      return;
    }

    const { id, documentId } = action.payload;

    // Make request to API route instead of direct Strapi client
    const response = yield call(fetch, `/api/referrer-links/${documentId}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    yield put(actions.deleteLinkSuccess(id));
  } catch (error: any) {
    console.error("Error deleting referrer link:", error);
    yield put(
      actions.setError(error.message || "Failed to delete referrer link")
    );
  }
}

function* handleFetchShortLink(
  action: PayloadAction<{ shortCode: string }>
): Generator<any, void, any> {
  try {
    const { shortCode } = action.payload;

    // Make request to API route instead of direct Strapi client
    const response = yield call(
      fetch,
      `/api/referrer-links/short/${shortCode}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error("SHORT_LINK_NOT_FOUND");
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = yield response.json();

    if (result.success && result.data?.data?.url) {
      yield put(actions.fetchShortLinkSuccess(result.data.data));

      // Set referral cookies for short link tracking
      if (typeof window !== "undefined") {
        try {
          const { setReferralData } = yield import('@/utils/cookies');
          const shortLinkData = result.data.data;

          console.log('🔄 [Short Link] Setting referral cookies:', {
            shortLink: shortLinkData.short_link,
            referralCode: shortLinkData.referrer?.referral_code,
            referralUrl: shortLinkData.url
          });

          // Set comprehensive referral data including short link
          setReferralData({
            shortLink: shortLinkData.short_link,
            referralCode: shortLinkData.referrer?.referral_code,
            referralUrl: shortLinkData.url
          });
        } catch (cookieError) {
          console.warn('🔄 [Short Link] Could not set referral cookies:', cookieError);
        }

        // Navigate to the URL - use window.location.href for external navigation
        console.log(`Redirecting to: ${result.data.data.url}`);
        window.location.href = result.data.data.url;
      }
    } else {
      throw new Error("SHORT_LINK_NOT_FOUND");
    }
  } catch (error: any) {
    console.error("Error fetching short link:", error);

    // Check if it's a 404 error or specific error message
    if (
      error.message?.includes("404") ||
      error.response?.status === 404 ||
      error.message === "SHORT_LINK_NOT_FOUND"
    ) {
      yield put(actions.fetchShortLinkFailure("SHORT_LINK_NOT_FOUND"));
    } else {
      yield put(
        actions.fetchShortLinkFailure(
          error.message || "Failed to fetch short link"
        )
      );
    }
  }
}

export default function* referrerLinksSaga() {
  yield takeLatest(actions.fetchLinks.type, handleFetchLinks);
  yield takeLatest(actions.createLink.type, handleCreateLink);
  yield takeLatest(actions.updateLink.type, handleUpdateLink);
  yield takeLatest(actions.deleteLink.type, handleDeleteLink);
  yield takeLatest(actions.fetchShortLink.type, handleFetchShortLink);
}
