import { call, put, takeEvery } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import axios from "axios";
import { actions } from "./transaction.slice";

function* fetchTransactionsSaga(
  action: PayloadAction<{
    page?: number;
    pageSize?: number;
    search?: string;
    sort?: string;
  }>
): Generator<any, void, any> {
  try {
    const { page = 1, pageSize = 10, search = "", sort = "createdAt:DESC" } = action.payload;

    // Get admin token from localStorage
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      throw new Error("Admin authentication required");
    }

    const response = yield call(axios.get, "/api/admin/transactions", {
      params: {
        page,
        pageSize,
        search,
        sort,
      },
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    yield put(
      actions.fetchTransactionsSuccess({
        data: response.data.data,
        meta: response.data.meta,
      })
    );
  } catch (error: any) {
    console.error("Error fetching transactions:", error);
    yield put(
      actions.fetchTransactionsFailure(
        error.response?.data?.error || error.message || "Failed to fetch transactions"
      )
    );
  }
}

export function* transactionSaga() {
  yield takeEvery(actions.fetchTransactionsRequest.type, fetchTransactionsSaga);
}
