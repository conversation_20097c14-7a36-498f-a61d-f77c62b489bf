import { call, put, takeEvery } from "redux-saga/effects";
import { actions } from "./payout.slice";
import { PayloadAction } from "@reduxjs/toolkit";
import axios from "axios";

function* fetchPayoutOverviewSaga(): Generator<any, void, any> {
  try {
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      yield put(actions.fetchPayoutOverviewFailure("Authentication required"));
      return;
    }

    // Call the API proxy directly
    const response = yield call(fetch, "/api/payouts?type=overview", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = yield response.json();
      yield put(
        actions.fetchPayoutOverviewFailure(
          errorData.error || "Failed to fetch payout overview"
        )
      );
      return;
    }

    const data = yield response.json();
    yield put(actions.fetchPayoutOverviewSuccess(data.data));
  } catch (error: any) {
    yield put(
      actions.fetchPayoutOverviewFailure(
        error.message || "Failed to fetch payout overview"
      )
    );
  }
}

function* fetchPayoutHistorySaga(action: any): Generator<any, void, any> {
  try {
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      yield put(actions.fetchPayoutHistoryFailure("Authentication required"));
      return;
    }

    const { page = 1, pageSize = 10 } = action.payload || {};

    // Build query parameters
    const queryParams = new URLSearchParams({
      "pagination[page]": page.toString(),
      "pagination[pageSize]": pageSize.toString(),
      sort: "createdAt:desc", // Sort by newest first
    });

    // Call the API proxy directly
    const response = yield call(fetch, `/api/payouts?${queryParams}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = yield response.json();
      yield put(
        actions.fetchPayoutHistoryFailure(
          errorData.error || "Failed to fetch payout history"
        )
      );
      return;
    }

    const data = yield response.json();
    yield put(actions.fetchPayoutHistorySuccess(data));
  } catch (error: any) {
    yield put(
      actions.fetchPayoutHistoryFailure(
        error.message || "Failed to fetch payout history"
      )
    );
  }
}

// Admin payout saga functions
function* fetchAdminPayoutsSaga(
  action: PayloadAction<{
    payout_status?: "pending" | "approved" | "completed";
    page?: number;
    pageSize?: number;
    search?: string;
  }>
): Generator<any, void, any> {
  try {
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      yield put(actions.fetchAdminPayoutsFailure("Authentication required"));
      return;
    }

    const { payout_status, page, pageSize, search } = action.payload;

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (payout_status) queryParams.append("status", payout_status);
    if (page) queryParams.append("page", page.toString());
    if (pageSize) queryParams.append("pageSize", pageSize.toString());
    if (search) queryParams.append("search", search);

    // Call the API proxy
    const response = yield call(
      axios.get,
      `/api/admin/payouts?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    yield put(actions.fetchAdminPayoutsSuccess(response.data));
  } catch (error: any) {
    console.error("Fetch admin payouts saga error:", error);
    yield put(
      actions.fetchAdminPayoutsFailure(
        error.response?.data?.error ||
          error.message ||
          "Failed to fetch admin payouts"
      )
    );
  }
}

function* approvePayoutSaga(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      yield put(actions.approvePayoutFailure("Authentication required"));
      return;
    }

    const documentId = action.payload;

    // Call the API proxy
    const response = yield call(
      axios.put,
      `/api/admin/payouts/${documentId}/approve`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    yield put(actions.approvePayoutSuccess(response.data));
  } catch (error: any) {
    console.error("Approve payout saga error:", error);
    yield put(
      actions.approvePayoutFailure(
        error.response?.data?.error ||
          error.message ||
          "Failed to approve payout"
      )
    );
  }
}

function* markAsPaidSaga(
  action: PayloadAction<string | string[]>
): Generator<any, void, any> {
  try {
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      yield put(actions.markAsPaidFailure("Authentication required"));
      return;
    }

    const documentIds = action.payload;

    // Call the API proxy
    const response = yield call(
      axios.put,
      `/api/admin/payouts/mark-as-paid`,
      { documentIds },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    // The response.data contains { data: [...] } from StrapiAdminClient
    const updatedPayouts = response.data.data || [];
    yield put(actions.markAsPaidSuccess(updatedPayouts));
  } catch (error: any) {
    console.error("Mark as paid saga error:", error);
    yield put(
      actions.markAsPaidFailure(
        error.response?.data?.error ||
          error.message ||
          "Failed to mark payouts as paid"
      )
    );
  }
}

function* archivePayoutSaga(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      yield put(actions.archivePayoutFailure("Authentication required"));
      return;
    }

    const documentId = action.payload;

    // Call the API proxy
    const response = yield call(
      axios.put,
      `/api/admin/payouts/${documentId}/archive`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    yield put(actions.archivePayoutSuccess(response.data));
  } catch (error: any) {
    console.error("Archive payout saga error:", error);
    yield put(
      actions.archivePayoutFailure(
        error.response?.data?.error ||
          error.message ||
          "Failed to archive payout"
      )
    );
  }
}

function* exportPayoutsSaga(
  action: PayloadAction<{
    payout_status?: "pending" | "approved" | "completed";
    format?: "csv" | "excel";
    search?: string;
  }>
): Generator<any, void, any> {
  try {
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      yield put(actions.exportPayoutsFailure("Authentication required"));
      return;
    }

    const { payout_status, format, search } = action.payload;

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (payout_status) queryParams.append("status", payout_status);
    if (format) queryParams.append("format", format);
    if (search) queryParams.append("search", search);

    // Call the API proxy
    const response = yield call(
      axios.get,
      `/api/admin/payouts/export?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        responseType: "blob",
      }
    );

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;

    const filename = `payouts_${payout_status || "all"}_${
      new Date().toISOString().split("T")[0]
    }.${format || "csv"}`;
    link.setAttribute("download", filename);

    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    yield put(actions.exportPayoutsSuccess());
  } catch (error: any) {
    console.error("Export payouts saga error:", error);
    yield put(
      actions.exportPayoutsFailure(
        error.response?.data?.error ||
          error.message ||
          "Failed to export payouts"
      )
    );
  }
}

function* createPayoutSaga(
  action: PayloadAction<{
    partnerId: string;
    amount: number;
    method: "paypal" | "bank transfer";
    payout_status: "pending" | "approved" | "completed";
  }>
): Generator<any, void, any> {
  try {
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;

    if (!token) {
      yield put(actions.createPayoutFailure("Authentication required"));
      return;
    }

    const { partnerId, amount, method, payout_status } = action.payload;

    // Call the API proxy
    const response = yield call(
      axios.post,
      `/api/admin/payouts`,
      {
        partnerId,
        amount,
        method,
        payout_status,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    yield put(actions.createPayoutSuccess());
  } catch (error: any) {
    console.error("Create payout error:", error);
    const errorMessage =
      error.response?.data?.error?.message ||
      error.response?.data?.message ||
      "Failed to create payout";
    yield put(actions.createPayoutFailure(errorMessage));
  }
}

export default function* payoutSaga() {
  yield takeEvery(actions.fetchPayoutOverview.type, fetchPayoutOverviewSaga);
  yield takeEvery(actions.fetchPayoutHistory.type, fetchPayoutHistorySaga);

  // Admin payout sagas
  yield takeEvery(actions.fetchAdminPayoutsRequest.type, fetchAdminPayoutsSaga);
  yield takeEvery(actions.approvePayoutRequest.type, approvePayoutSaga);
  yield takeEvery(actions.markAsPaidRequest.type, markAsPaidSaga);
  yield takeEvery(actions.archivePayoutRequest.type, archivePayoutSaga);
  yield takeEvery(actions.exportPayoutsRequest.type, exportPayoutsSaga);
  yield takeEvery(actions.createPayoutRequest.type, createPayoutSaga);
}
