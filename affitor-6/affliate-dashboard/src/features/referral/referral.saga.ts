import { call, put, takeEvery } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import axios from "axios";
import { actions } from "./referral.slice";

function* fetchReferralsSaga(
  action: PayloadAction<{
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    isAdmin?: boolean;
    sort?: string;
    username?: string;
    email?: string;
    createdAtFrom?: string;
    createdAtTo?: string;
    totalPaidFrom?: string;
    totalPaidTo?: string;
    referrerId?: string;
  }>
): Generator<any, void, any> {
  try {
    const {
      page = 1,
      pageSize = 25,
      search = "",
      status = "",
      isAdmin = false,
      sort,
      username,
      email,
      createdAtFrom,
      createdAtTo,
      totalPaidFrom,
      totalPaidTo,
      referrerId,
    } = action.payload;

    console.log("🚀 Redux Saga - fetchReferralsSaga called with payload:", action.payload);

    // Get appropriate token based on admin status
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem(isAdmin ? "admin_token" : "auth_token")
        : null;

    if (!token) {
      throw new Error("Authentication required");
    }

    // Use appropriate API endpoint
    const apiUrl = isAdmin ? "/api/admin/referrals" : "/api/referrals";

    // Build params object with all filters
    const params: any = {
      page,
      pageSize,
      search,
      status,
      ...(sort && { sort }),
    };

    // Add advanced filters if they exist
    if (username) params.username = username;
    if (email) params.email = email;
    if (createdAtFrom) params.createdAtFrom = createdAtFrom;
    if (createdAtTo) params.createdAtTo = createdAtTo;
    if (totalPaidFrom) params.totalPaidFrom = totalPaidFrom;
    if (totalPaidTo) params.totalPaidTo = totalPaidTo;
    if (referrerId) {
      console.log("🎯 Redux Saga - Adding referrerId to API call:", referrerId);
      params.referrerId = referrerId;
    }

    console.log("📤 Redux Saga - Final API params:", params);

    const response = yield call(axios.get, apiUrl, {
      params,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    console.log("📥 Redux Saga - API response received:", response.data);
    yield put(actions.fetchReferralsSuccess(response.data));
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      "Failed to fetch referrals";
    yield put(actions.fetchReferralsFailure(errorMessage));
  }
}

export default function* referralSaga() {
  yield takeEvery(actions.fetchReferralsRequest.type, fetchReferralsSaga);
}
