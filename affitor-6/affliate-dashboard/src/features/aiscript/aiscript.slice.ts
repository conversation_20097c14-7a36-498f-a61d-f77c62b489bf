import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '@/store';

export interface QuickReply {
  label: string;
  content: string;
  promptId?: number | undefined; // Optional promptId for quick replies
}

export interface Message {
  type: 'user' | 'ai';
  content: string;
  quickReplies?: QuickReply[];
  copyable?: boolean;
}

export interface Prompt {
  id: number;
  documentId: string;
  title: string;
  content: string;
  description: string | null;
}

export interface UserPromptAction {
  displayText: string;
  content: string;
  promptId?: number; // Add promptId as an optional field
}

interface AiscriptState {
  messages: Message[];
  isLoading: boolean;
  error: string | null;
  isOpen: boolean;
  quickReplies: QuickReply[];
  prompts: Prompt[];
  promptsLoading: boolean;
  sessionId: string | null;
}

const initialState: AiscriptState = {
  messages: [],
  isLoading: false,
  error: null,
  isOpen: false,
  quickReplies: [],
  prompts: [],
  promptsLoading: false,
  sessionId: null
};

const aiscriptSlice = createSlice({
  name: 'aiscript',
  initialState,
  reducers: {
    sendMessage: (state, action: PayloadAction<string>) => {
      // Add user message to messages
      state.messages.push({ type: 'user', content: action.payload });
      state.error = null;
    },
    setMessage: (state, action: PayloadAction<Message>) => {
      state.messages.push(action.payload);
    },
    setMessageWithQuickReplies: (state, action: PayloadAction<{content: string, quickReplies: QuickReply[]}>) => {
      state.messages.push({
        type: 'ai',
        content: action.payload.content,
        quickReplies: action.payload.quickReplies
      });
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      
      if (action.payload) {
        state.messages.push({
          type: 'ai',
          content: "I'm having trouble connecting. Please try again later."
        });
      }
    },
    clearMessages: (state) => {
      state.messages = [];
    },
    openAIScript: (state) => {
      state.isOpen = true;
    },
    closeAIScript: (state) => {
      state.isOpen = false;
    },
    toggleScript: (state) => {
      state.isOpen = !state.isOpen;
    },
    setQuickReplies: (state, action: PayloadAction<QuickReply[]>) => {
      state.quickReplies = action.payload;
    },
    fetchPrompts: (state) => {
      state.promptsLoading = true;
    },
    setPrompts: (state, action: PayloadAction<Prompt[]>) => {
      state.prompts = action.payload;
      state.promptsLoading = false;
      
      // Convert prompts to quick replies format
      const quickReplies: QuickReply[] = action.payload.map(prompt => ({
        label: prompt.title,
        content: prompt.content
      }));
      
      state.quickReplies = quickReplies;
    },
    setPromptsError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.promptsLoading = false;
    },
    endSession: (state) => {
      // This will be handled by saga
      state.isOpen = false;
      state.messages = [];
      state.quickReplies = [];
    },
    sendUserPrompt: (state, action: PayloadAction<UserPromptAction>) => {
      console.log(action.payload);
      // Add user message with only the display text
      console.log('LOG-state.messages', state.messages);
      state.messages.push({ 
        type: 'user', 
        content: action.payload.displayText || action.payload.content,
      }); 

      console.log('LOG-state.messages', state.messages);
      state.error = null;
    },
    setSessionId: (state, action: PayloadAction<string | null>) => {
      state.sessionId = action.payload;
    },
  },
});

export const { actions } = aiscriptSlice; 

// Define base selector
const selectAiscriptState = (state: RootState) => state.aiscript;

// Create and export selectors
export const selectAiscriptMessages = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.messages
);

export const selectAiscriptLoading = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.isLoading
);

export const selectAiscriptError = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.error
);

export const selectAiscriptOpen = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.isOpen
);

export const selectAiscriptQuickReplies = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.quickReplies
);

export const selectAiscriptPrompts = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.prompts
);

export const selectPromptsLoading = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.promptsLoading
);

export const selectSessionId = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.sessionId
);

// Export the reducer
export const aiscriptReducer = aiscriptSlice.reducer;
export default aiscriptReducer;
