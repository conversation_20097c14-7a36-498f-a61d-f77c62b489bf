import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight, AlertCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import axios from "axios";

export const UpgradeSuccess = () => {
  const router = useRouter();
  const { session_id, transaction_id } = router.query;
  const [isVisible, setIsVisible] = useState(false);
  const [confirmationStatus, setConfirmationStatus] = useState<
    "loading" | "success" | "error"
  >("loading");
  const [errorMessage, setErrorMessage] = useState<string>("");

  useEffect(() => {
    setIsVisible(true);
  }, []);

  useEffect(() => {
    async function confirmCheckout() {
      if (transaction_id) {
        setConfirmationStatus("success");
      } else if (session_id && typeof session_id === "string") {
        try {
          // Get token from localStorage
          const token =
            typeof window !== "undefined"
              ? localStorage.getItem("auth_token")
              : null;

          // Create headers with token if available
          const headers: Record<string, string> = {};
          if (token) {
            headers["Authorization"] = `Bearer ${token}`;
          }

          // Make request to our API endpoint
          await axios.get(
            `/api/subscription-tiers/confirm-checkout/${session_id}`,
            { headers }
          );

          setConfirmationStatus("success");
        } catch (error: any) {
          console.error("Error confirming checkout:", error);
          setConfirmationStatus("error");
          setErrorMessage(
            error.response?.data?.message ||
              "Failed to confirm your subscription. Please contact support."
          );
        }
      } else if (router.isReady && !session_id) {
        setConfirmationStatus("error");
        setErrorMessage("Missing session information. Please contact support.");
      }
    }

    if (router.isReady) {
      confirmCheckout();
    }
  }, [session_id, router.isReady]);

  const renderContent = () => {
    if (confirmationStatus === "loading") {
      return (
        <CardContent className="text-center pt-2">
          <p className="text-muted-foreground">
            Processing your subscription...
          </p>
        </CardContent>
      );
    } else if (confirmationStatus === "error") {
      return (
        <>
          <CardHeader className="pb-2 text-center">
            <div
              className={`flex justify-center mb-6 transition-all duration-1000 delay-300 ${
                isVisible
                  ? "opacity-100 transform scale-100"
                  : "opacity-0 transform scale-50"
              }`}
            >
              <div className="h-20 w-20 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                <AlertCircle className="h-12 w-12 text-red-600 dark:text-red-400" />
              </div>
            </div>

            <CardTitle className="text-2xl font-bold text-foreground">
              Subscription Error
            </CardTitle>
          </CardHeader>

          <CardContent className="text-center pt-2">
            <p className="text-muted-foreground">{errorMessage}</p>
          </CardContent>
        </>
      );
    } else {
      return (
        <>
          <CardHeader className="pb-2 text-center">
            <div
              className={`flex justify-center mb-6 transition-all duration-1000 delay-300 ${
                isVisible
                  ? "opacity-100 transform scale-100"
                  : "opacity-0 transform scale-50"
              }`}
            >
              <div className="h-20 w-20 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                <CheckCircle className="h-12 w-12 text-green-600 dark:text-green-400" />
              </div>
            </div>

            <CardTitle className="text-2xl font-bold text-foreground">
              Upgrade Successful!
            </CardTitle>
          </CardHeader>

          <CardContent className="text-center pt-2">
            <p className="text-muted-foreground">
              Your account has been upgraded successfully.
            </p>
          </CardContent>
        </>
      );
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-background/95 p-4">
      <div
        className={`max-w-md w-full transition-all duration-700 ${
          isVisible
            ? "opacity-100 transform translate-y-0"
            : "opacity-0 transform -translate-y-8"
        }`}
      >
        <Card
          className={`border-2 ${
            confirmationStatus === "error"
              ? "border-red-100 dark:border-red-900/50"
              : confirmationStatus === "success"
              ? "border-green-100 dark:border-green-900/50"
              : "border-blue-100 dark:border-blue-900/50"
          } shadow-lg overflow-hidden`}
        >
          <div
            className={`absolute top-0 right-0 w-24 h-24 -mt-8 -mr-8 ${
              confirmationStatus === "error"
                ? "bg-red-400 dark:bg-red-600"
                : confirmationStatus === "success"
                ? "bg-green-400 dark:bg-green-600"
                : "bg-blue-400 dark:bg-blue-600"
            } rounded-full opacity-20`}
          ></div>

          <div className="relative">
            {renderContent()}

            <CardFooter className="flex flex-col gap-3 pt-4">
              <Button
                className="w-full"
                variant="default"
                onClick={() => router.push("/dashboard")}
              >
                Continue to Dashboard
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default UpgradeSuccess;
