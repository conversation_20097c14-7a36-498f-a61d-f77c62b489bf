import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import {
  selectSubscriptionTiers,
  selectSubscriptionTiersLoading,
  selectSubscriptionTiersError,
  selectCurrentSubscription,
} from "@/features/selectors";
import { subscriptionTierActions, userActions } from "@/features/rootActions";
import { AppDispatch } from "@/store";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Shield, Loader } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import PricingCard from "./PricingCard";
import CompareTier from "./compare-tier";

export const Upgrade = () => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const [redirecting, setRedirecting] = useState(false);
  const [isSubscribing, setIsSubscribing] = useState(false); // Add subscription loading state

  const subscriptionTiers = useSelector(selectSubscriptionTiers);
  const loading = useSelector(selectSubscriptionTiersLoading);
  const error = useSelector(selectSubscriptionTiersError);
  console.log("Error:", error);
  const currentSubscription = useSelector(selectCurrentSubscription);
  const [billingPeriod, setBillingPeriod] = useState<
    "monthly" | "quarterly" | "yearly"
  >("yearly");
  const [slideDirection, setSlideDirection] = useState<"left" | "right">(
    "right"
  );

  useEffect(() => {
    // Fetch subscription tiers
    dispatch(subscriptionTierActions.fetchSubscriptionTiers());

    // Fetch user profile data to ensure it's available on page refresh
    if (!currentSubscription) dispatch(userActions.fetchUserMe());
  }, [dispatch]);

  // Handle authentication errors and redirect
  // useEffect(() => {
  //   const isAuthError =
  //     error &&
  //     (error.includes("401") ||
  //       error.toLowerCase().includes("authentication required") ||
  //       error.toLowerCase().includes("unauthorized"));

  //   if (isAuthError) {
  //     setRedirecting(true);
  //     const timer = setTimeout(() => {
  //       router.replace(
  //         `/authentication?redirect=${encodeURIComponent(router.asPath)}`
  //       );
  //     }, 1000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [error, router]);

  const handleBillingPeriodChange = (
    value: "monthly" | "quarterly" | "yearly"
  ) => {
    // Set slide direction based on which way we're moving
    if (value === "yearly") {
      setSlideDirection("right");
    } else if (value === "monthly") {
      setSlideDirection("left");
    } else if (value === "quarterly") {
      // For quarterly, determine direction based on current period
      setSlideDirection(billingPeriod === "yearly" ? "left" : "right");
    }

    setBillingPeriod(value);
  };

  const handleSubscribe = (tierId: number, paymentMethod: string) => {
    setIsSubscribing(true); // Set loading state when subscribing

    dispatch(
      subscriptionTierActions.subscribeTier({
        tierId,
        paymentMethod,
      })
    );
  };

  // Group subscription tiers by display name and interval
  const groupedTiers: any = subscriptionTiers.reduce((acc: any, tier: any) => {
    // Default to 'month' if not specified
    const interval = tier.stripe_recurring_interval || "month";
    const key = tier.display_name || "";

    if (!acc[key]) {
      acc[key] = { month: null, year: null };
    }

    // Assign tier to its interval category
    if (interval === "month" || interval === "week" || interval === "day") {
      acc[key].month = tier;
    } else if (interval === "year") {
      acc[key].year = tier;
    }

    return acc;
  }, {});

  // Filter for tiers that have both monthly and yearly options
  const tierPairs = Object.values(groupedTiers).filter(
    (pair: any) => pair.month && pair.year
  ) as { month: any; year: any }[];

  // Calculate savings between monthly and yearly plans
  const calculateSavings = (monthlyTier: any, yearlyTier: any) => {
    if (!monthlyTier || !yearlyTier) return null;

    const monthlyAnnualCost = monthlyTier.price * 12;
    const yearlyCost = yearlyTier.price;

    if (monthlyAnnualCost > 0 && yearlyCost > 0) {
      const savingsAmount = monthlyAnnualCost - yearlyCost;
      const savingsPercent = (savingsAmount / monthlyAnnualCost) * 100;

      return {
        amount: savingsAmount.toFixed(2),
        percent: savingsPercent.toFixed(0),
      };
    }

    return null;
  };

  // Get displayed tiers based on billing period
  const getDisplayedTiers = () => {
    if (billingPeriod === "yearly") {
      return subscriptionTiers.filter(
        (tier) => tier.stripe_recurring_interval === "year"
      );
    } else if (billingPeriod === "quarterly") {
      return subscriptionTiers.filter(
        (tier) => tier.stripe_recurring_interval === "quarter"
      );
    } else {
      return subscriptionTiers.filter(
        (tier) =>
          !tier.stripe_recurring_interval ||
          tier.stripe_recurring_interval === "month"
      );
    }
  };

  const displayedTiers = getDisplayedTiers();

  // Get pricing and period display info
  const getPricingInfo = (tier: any) => {
    const isYearly = tier.stripe_recurring_interval === "year";
    const isQuarterly = tier.stripe_recurring_interval === "Every 3 months";

    return {
      price: tier.price,
      period: isYearly
        ? "year"
        : isQuarterly
        ? "quarter"
        : tier.duration_days
        ? `${tier.duration_days} days`
        : "month",
      billedAs: isYearly ? "year" : isQuarterly ? "quarter" : "month",
    };
  };

  // Handler for "Get Started" button in CompareTier
  const handleGetStarted = () => {
    const premiumTier = subscriptionTiers.find(
      (tier) =>
        tier.display_name?.toLowerCase().includes("premium") &&
        (billingPeriod === "yearly"
          ? tier.stripe_recurring_interval === "year"
          : billingPeriod === "quarterly"
          ? tier.stripe_recurring_interval === "quarter"
          : tier.stripe_recurring_interval === "month" ||
            !tier.stripe_recurring_interval)
    );
    if (premiumTier) {
      handleSubscribe(premiumTier.id, "stripe");
    }
  };

  // Handler for "Get Pro" button in CompareTier
  const handleGetPro = () => {
    const proTier = subscriptionTiers.find(
      (tier) =>
        tier.display_name?.toLowerCase().includes("pro") &&
        (billingPeriod === "yearly"
          ? tier.stripe_recurring_interval === "year"
          : billingPeriod === "quarterly"
          ? tier.stripe_recurring_interval === "quarter"
          : tier.stripe_recurring_interval === "month" ||
            !tier.stripe_recurring_interval)
    );
    if (proTier) {
      handleSubscribe(proTier.id, "stripe");
    }
  };

  // Handler for "Start Free" button in CompareTier
  const handleStartFree = () => {
    const basicTier = subscriptionTiers.find((tier) =>
      tier.display_name?.toLowerCase().includes("basic")
    );
    if (basicTier) {
      handleSubscribe(basicTier.id, "stripe");
    }
  };

  // Check if this is an authentication error
  const isAuthError =
    error &&
    (error.includes("401") ||
      error.toLowerCase().includes("authentication required") ||
      error.toLowerCase().includes("unauthorized"));

  // If there's an authentication error, show a message and redirect
  if (isAuthError) {
    return (
      <div className="max-w-6xl mx-auto p-6 flex flex-col items-center justify-center min-h-[60vh]">
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6 max-w-md w-full text-center">
          <div className="flex justify-center mb-4">
            <AlertCircle className="h-12 w-12 text-red-500" />
          </div>
          <h2 className="text-xl font-semibold mb-2 dark:text-white">
            Authentication Error
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Your session has expired or you are not authenticated.
          </p>
          {redirecting ? (
            <p className="text-sm text-blue-600 dark:text-blue-400">
              Redirecting to sign in...
            </p>
          ) : (
            <Button
              onClick={() =>
                router.replace(
                  `/authentication?redirect=${encodeURIComponent(
                    router.asPath
                  )}`
                )
              }
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Sign In
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Background pattern - updated for dark mode */}
      <div className="absolute inset-0 -z-10 opacity-5 dark:opacity-10 pointer-events-none">
        <div className="absolute inset-0 bg-grid-slate-400/[0.05] dark:bg-grid-slate-100/[0.03] bg-[size:20px_20px] mask-gradient-to-b" />
      </div>

      <div className="container mx-auto py-8 px-4 md:py-12">
        <div className="text-center mb-6 md:mb-10">
          <h1 className="text-3xl md:text-4xl font-extrabold tracking-tight mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-blue-700 dark:from-blue-400 dark:to-blue-600 leading-tight">
            Find Your Perfect Plan
          </h1>
          <p className="text-muted-foreground text-sm md:text-base max-w-3xl mx-auto mb-4">
            Discover the ideal plan to supercharge your affiliate hunt. Start
            free or unlock premium perks.
          </p>
        </div>

        {error && !isAuthError && (
          <Alert variant="destructive" className="mb-6 max-w-4xl mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error loading subscription plans</AlertTitle>
            <AlertDescription>
              {error}. Please try refreshing the page or contact support if the
              issue persists.
            </AlertDescription>
          </Alert>
        )}

        {/* Billing Toggle - Updated for Dark Mode */}
        <div className="flex justify-center mb-6">
          <div className="relative w-full max-w-md">
            <Tabs
              defaultValue="yearly"
              className="w-full"
              onValueChange={(value) =>
                handleBillingPeriodChange(
                  value as "monthly" | "quarterly" | "yearly"
                )
              }
            >
              <div className="flex items-center justify-center relative mb-8">
                <TabsList className="grid w-full max-w-xs grid-cols-2 p-1 rounded-full bg-muted dark:bg-muted/40 h-10">
                  <TabsTrigger
                    value="monthly"
                    className="rounded-full text-xs md:text-sm data-[state=active]:bg-black dark:data-[state=active]:bg-white data-[state=active]:text-white dark:data-[state=active]:text-black h-8"
                  >
                    Monthly
                  </TabsTrigger>
                  <TabsTrigger
                    value="yearly"
                    className="rounded-full text-xs md:text-sm data-[state=active]:bg-black dark:data-[state=active]:bg-white data-[state=active]:text-white dark:data-[state=active]:text-black h-8 relative"
                  >
                    Yearly
                    {/* Mobile-only slanted badge */}
                    <Badge
                      variant="outline"
                      className="absolute -right-[20px] -top-[5px] sm:hidden rotate-24 bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800 text-[10px] px-1.5 py-0.5 whitespace-nowrap"
                    >
                      Best Value
                    </Badge>
                  </TabsTrigger>
                </TabsList>
                {/* Desktop standard badge */}
                <Badge
                  variant="outline"
                  className="absolute hidden md:block -right-10 bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800 text-xs"
                >
                  Best Value
                </Badge>
              </div>

              <div className="relative overflow-hidden">
                <TabsContent
                  value="monthly"
                  className={`mt-0 transition-transform duration-500 ease-in-out ${
                    slideDirection === "left" && billingPeriod === "monthly"
                      ? "translate-x-0"
                      : "-translate-x-full"
                  } absolute w-full ${
                    billingPeriod === "monthly" ? "relative" : ""
                  }`}
                />
                <TabsContent
                  value="quarterly"
                  className={`mt-0 transition-transform duration-500 ease-in-out ${
                    billingPeriod === "quarterly"
                      ? "translate-x-0"
                      : billingPeriod === "yearly"
                      ? "-translate-x-full"
                      : "translate-x-full"
                  } absolute w-full ${
                    billingPeriod === "quarterly" ? "relative" : ""
                  }`}
                />
                <TabsContent
                  value="yearly"
                  className={`mt-0 transition-transform duration-500 ease-in-out ${
                    slideDirection === "right" && billingPeriod === "yearly"
                      ? "translate-x-0"
                      : "translate-x-full"
                  } absolute w-full ${
                    billingPeriod === "yearly" ? "relative" : ""
                  }`}
                />
              </div>
            </Tabs>
          </div>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 md:gap-4 max-w-6xl mx-auto place-items-center">
            {[1, 2, 3].map((i) => (
              <Card
                key={i}
                className="relative h-[500px] flex flex-col animate-pulse dark:border-muted max-w-[400px] w-full md:w-full"
              >
                <Skeleton className="h-full w-full dark:bg-muted/30" />
              </Card>
            ))}
          </div>
        ) : (
          <div
            className={`grid grid-cols-1 md:grid-cols-3 gap-2 md:gap-4 max-w-6xl mx-auto place-items-stretch transition-opacity duration-300 ${
              billingPeriod === "yearly" ? "opacity-100" : "opacity-100"
            }`}
          >
            {displayedTiers.slice(0, 3).map((tier, index) => {
              const pricing = getPricingInfo(tier);
              // Add custom styling for middle item (Pro plan)
              const isMiddleItem = index === 1;

              return (
                <div
                  key={tier.id}
                  className={`w-full md:w-full group hover:-translate-y-1 transition-transform duration-300 h-full ${
                    isMiddleItem ? "md:z-10" : ""
                  }`}
                >
                  <PricingCard
                    tier={tier}
                    index={index}
                    pricing={pricing}
                    billingPeriod={billingPeriod}
                    subscriptionTiers={subscriptionTiers}
                    currentSubscription={currentSubscription}
                    onSubscribe={handleSubscribe}
                  />
                </div>
              );
            })}
          </div>
        )}

        {/* Compare Tier Section */}
        <div className="mt-16 mb-8">
          <CompareTier
            onGetStarted={handleGetStarted}
            onStartFree={handleStartFree}
            onGetPro={handleGetPro}
            isLoading={isSubscribing}
            billingPeriod={billingPeriod}
            subscriptionTiers={subscriptionTiers}
          />
        </div>

        <div className="mt-10 text-center">
          <div className="bg-muted/40 dark:bg-muted/10 rounded-lg p-4 md:p-6 max-w-3xl mx-auto border border-transparent dark:border-muted/20">
            <h3 className="text-lg font-medium mb-1">
              Need help choosing the right plan?
            </h3>
            <p className="text-muted-foreground mb-3 text-sm">
              Our team is ready to help you find the perfect solution for your
              needs
            </p>
            <Button
              variant="secondary"
              size="sm"
              className="mt-1 dark:bg-muted/30 dark:hover:bg-muted/50"
              onClick={() => window.open("mailto:<EMAIL>", "_blank")}
            >
              <Shield className="mr-2 h-4 w-4" /> Contact Support
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Upgrade;
