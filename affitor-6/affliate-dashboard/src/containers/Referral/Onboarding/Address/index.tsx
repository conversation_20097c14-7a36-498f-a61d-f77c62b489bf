import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import SelectCountry from "@/components/SelectCountry";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowLef<PERSON>, ArrowRight, Loader, SkipForward } from "lucide-react";

export interface AddressFormData {
  streetAddress: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

interface AddressStepProps {
  data: AddressFormData;
  onChange: (data: AddressFormData) => void;
  onNext: () => void;
  onBack: () => void;
  onSkip?: () => void; // New prop for skipping this step
  errors: { [key: string]: string };
  isValid: boolean;
  isSubmitting?: boolean;
  isLoading?: boolean;
}

const AddressStep: React.FC<AddressStepProps> = ({
  data,
  onChange,
  onNext,
  onBack,
  onSkip,
  errors,
  isValid,
  isSubmitting = false,
  isLoading = false,
}) => {
  const handleChange = (field: keyof AddressFormData, value: string) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto border-t-4 border-primary/70 shadow-md relative">
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 dark:bg-gray-800/80 flex items-center justify-center z-10 rounded-b-lg">
          <div className="flex flex-col items-center">
            <Loader className="h-8 w-8 text-primary animate-spin mb-2" />
            <p className="text-sm text-primary">Loading address data...</p>
          </div>
        </div>
      )}
      <CardHeader className="bg-gradient-to-r from-primary/5 to-transparent">
        <div>
          <CardTitle>Address Information</CardTitle>
          <CardDescription>
            Tell us where you're located (optional)
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-6">
          <div>
            <label
              htmlFor="streetAddress"
              className="block text-sm font-medium mb-1 flex"
            >
              Street Address <span className="text-red-500 ml-1">*</span>
            </label>
            <div className="relative">
              <Input
                id="streetAddress"
                value={data.streetAddress}
                onChange={(e) => handleChange("streetAddress", e.target.value)}
                placeholder={isLoading ? "Loading..." : "123 Main Street"}
                className={errors.streetAddress ? "border-red-500" : ""}
                required
                disabled={isSubmitting || isLoading}
              />
              {isLoading && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Loader className="h-4 w-4 text-primary animate-spin" />
                </div>
              )}
            </div>
            {errors.streetAddress && (
              <p className="text-red-500 text-xs mt-1">{errors.streetAddress}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="city" className="block text-sm font-medium mb-1 flex">
                City <span className="text-red-500 ml-1">*</span>
              </label>
              <div className="relative">
                <Input
                  id="city"
                  value={data.city}
                  onChange={(e) => handleChange("city", e.target.value)}
                  placeholder={isLoading ? "Loading..." : "New York"}
                  className={errors.city ? "border-red-500" : ""}
                  required
                  disabled={isSubmitting || isLoading}
                />
                {isLoading && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Loader className="h-4 w-4 text-primary animate-spin" />
                  </div>
                )}
              </div>
              {errors.city && (
                <p className="text-red-500 text-xs mt-1">{errors.city}</p>
              )}
            </div>

            <div>
              <label htmlFor="state" className="block text-sm font-medium mb-1 flex">
                State/Province <span className="text-red-500 ml-1">*</span>
              </label>
              <div className="relative">
                <Input
                  id="state"
                  value={data.state}
                  onChange={(e) => handleChange("state", e.target.value)}
                  placeholder={isLoading ? "Loading..." : "NY"}
                  className={errors.state ? "border-red-500" : ""}
                  required
                  disabled={isSubmitting || isLoading}
                />
                {isLoading && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Loader className="h-4 w-4 text-primary animate-spin" />
                  </div>
                )}
              </div>
              {errors.state && (
                <p className="text-red-500 text-xs mt-1">{errors.state}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="zipCode"
                className="block text-sm font-medium mb-1 flex"
              >
                ZIP/Postal Code <span className="text-red-500 ml-1">*</span>
              </label>
              <div className="relative">
                <Input
                  id="zipCode"
                  value={data.zipCode}
                  onChange={(e) => handleChange("zipCode", e.target.value)}
                  placeholder={isLoading ? "Loading..." : "10001"}
                  className={errors.zipCode ? "border-red-500" : ""}
                  required
                  disabled={isSubmitting || isLoading}
                />
                {isLoading && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Loader className="h-4 w-4 text-primary animate-spin" />
                  </div>
                )}
              </div>
              {errors.zipCode && (
                <p className="text-red-500 text-xs mt-1">{errors.zipCode}</p>
              )}
            </div>

            <div>
              <label
                htmlFor="country"
                className="block text-sm font-medium mb-1 flex"
              >
                Country <span className="text-red-500 ml-1">*</span>
              </label>
              <div className="relative">
                <SelectCountry
                  value={data.country}
                  onValueChange={(value) => handleChange("country", value)}
                  id="country"
                  className={errors.country ? "border-red-500" : ""}
                />
                {isLoading && (
                  <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
                    <Loader className="h-4 w-4 text-primary animate-spin" />
                  </div>
                )}
              </div>
              {errors.country && (
                <p className="text-red-500 text-xs mt-1">{errors.country}</p>
              )}
            </div>
          </div>

          {errors.submission && (
            <div className="bg-red-50 text-red-600 p-3 rounded-lg text-sm">
              {errors.submission}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-6 bg-secondary/50">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex items-center gap-2"
          disabled={isSubmitting || isLoading}
        >
          <ArrowLeft className="w-4 h-4" /> Back
        </Button>
        
        <div className="flex gap-2">
          {onSkip && (
            <Button
              variant="ghost"
              onClick={onSkip}
              className="flex items-center gap-2"
              disabled={isSubmitting || isLoading}
            >
              Skip this step <SkipForward className="w-4 h-4" />
            </Button>
          )}

          <Button
            onClick={onNext}
            className="flex items-center gap-2 min-w-[120px]"
            disabled={!isValid || isSubmitting || isLoading}
          >
            {isSubmitting ? (
              <>
                <div className="h-4 w-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-1"></div>
                Saving...
              </>
            ) : (
              <>
                Continue <ArrowRight className="w-4 h-4" />
              </>
            )}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default AddressStep;
