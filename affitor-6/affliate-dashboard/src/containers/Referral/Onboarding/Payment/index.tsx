import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import SelectCountry from "@/components/SelectCountry";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowLeft, AlertCircle, Loader } from "lucide-react";

export type PaymentMethod = "paypal" | "bank-transfer";

export interface PayPalFormData {
  paypalEmail: string;
}

export interface BankTransferFormData {
  firstName: string;
  lastName: string;
  businessName?: string;
  country: string;
  city: string;
  state?: string;
  address: string;
  zipCode: string;
  accountNumber: string;
  swiftCode: string;
}

export interface PaymentFormData {
  method: PaymentMethod;
  paypal?: PayPalFormData;
  bankTransfer?: BankTransferFormData;
}

interface PaymentStepProps {
  data: PaymentFormData;
  onChange: (data: PaymentFormData) => void;
  onComplete: () => void;
  onBack: () => void;
  isSubmitting: boolean;
  errors: { [key: string]: string };
  isValid: boolean;
  isLoading?: boolean;
}

const PaymentStep: React.FC<PaymentStepProps> = ({
  data,
  onChange,
  onComplete,
  onBack,
  isSubmitting,
  errors,
  isValid,
  isLoading = false,
}) => {
  const handleMethodChange = (method: PaymentMethod) => {
    onChange({ ...data, method });
  };

  const handlePayPalChange = (paypalEmail: string) => {
    onChange({
      ...data,
      paypal: { paypalEmail },
    });
  };

  const handleBankTransferChange = (
    field: keyof BankTransferFormData,
    value: string
  ) => {
    onChange({
      ...data,
      bankTransfer: {
        ...data.bankTransfer!,
        [field]: value,
      },
    });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto border-t-4 border-primary/70 shadow-md relative">
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 dark:bg-gray-800/80 flex items-center justify-center z-10 rounded-b-lg">
          <div className="flex flex-col items-center">
            <Loader className="h-8 w-8 text-primary animate-spin mb-2" />
            <p className="text-sm text-primary">Loading payment information...</p>
          </div>
        </div>
      )}
      <CardHeader className="bg-gradient-to-r from-primary/5 to-transparent">
        <div>
          <CardTitle>Payment Method</CardTitle>
          <CardDescription>
            Choose how to receive affiliate payments (optional)
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-6">
          <div>
            <label
              htmlFor="paymentMethod"
              className="block text-sm font-medium mb-1"
            >
              Payment Method
            </label>
            <div className="relative">
              <Select
                value={data.method}
                onValueChange={handleMethodChange as (value: string) => void}
                disabled={isLoading || isSubmitting}
              >
                <SelectTrigger id="paymentMethod" className="w-full">
                  <SelectValue placeholder={isLoading ? "Loading..." : "Select payment method"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="paypal">PayPal (Recommended)</SelectItem>
                  <SelectItem value="bank-transfer">Bank Transfer</SelectItem>
                </SelectContent>
              </Select>
              {isLoading && (
                <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
                  <Loader className="h-4 w-4 text-primary animate-spin" />
                </div>
              )}
            </div>
          </div>

          {data.method === "paypal" ? (
            <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
              <div>
                <label
                  htmlFor="paypalEmail"
                  className="block text-sm font-medium mb-1"
                >
                  PayPal Email
                </label>
                <div className="relative">
                  <Input
                    id="paypalEmail"
                    value={data.paypal?.paypalEmail || ""}
                    onChange={(e) => handlePayPalChange(e.target.value)}
                    placeholder={isLoading ? "Loading..." : "<EMAIL>"}
                    className={errors.paypalEmail ? "border-red-500" : ""}
                    disabled={isLoading || isSubmitting}
                  />
                  {isLoading && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <Loader className="h-4 w-4 text-primary animate-spin" />
                    </div>
                  )}
                </div>
                {errors.paypalEmail && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.paypalEmail}
                  </p>
                )}
              </div>

              <div className="flex items-center mt-4 text-blue-600 bg-blue-100/50 p-3 rounded">
                <span className="text-yellow-500 mr-2">💡</span>
                <p className="text-sm">
                  Fastest and most secure payment method
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-6 border p-6 rounded-lg bg-secondary/50">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="bankFirstName"
                    className="block text-sm font-medium mb-1"
                  >
                    First Name
                  </label>
                  <div className="relative">
                    <Input
                      id="bankFirstName"
                      value={data.bankTransfer?.firstName || ""}
                      onChange={(e) =>
                        handleBankTransferChange("firstName", e.target.value)
                      }
                      className={errors.bankFirstName ? "border-red-500" : ""}
                      placeholder={isLoading ? "Loading..." : ""}
                      disabled={isLoading || isSubmitting}
                    />
                    {isLoading && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <Loader className="h-4 w-4 text-primary animate-spin" />
                      </div>
                    )}
                  </div>
                  {errors.bankFirstName && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.bankFirstName}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="bankLastName"
                    className="block text-sm font-medium mb-1"
                  >
                    Last Name
                  </label>
                  <div className="relative">
                    <Input
                      id="bankLastName"
                      value={data.bankTransfer?.lastName || ""}
                      onChange={(e) =>
                        handleBankTransferChange("lastName", e.target.value)
                      }
                      className={errors.bankLastName ? "border-red-500" : ""}
                      placeholder={isLoading ? "Loading..." : ""}
                      disabled={isLoading || isSubmitting}
                    />
                    {isLoading && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <Loader className="h-4 w-4 text-primary animate-spin" />
                      </div>
                    )}
                  </div>
                  {errors.bankLastName && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.bankLastName}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <label
                  htmlFor="businessName"
                  className="block text-sm font-medium mb-1"
                >
                  Business Name (Optional)
                </label>
                <Input
                  id="businessName"
                  value={data.bankTransfer?.businessName || ""}
                  onChange={(e) =>
                    handleBankTransferChange("businessName", e.target.value)
                  }
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="bankCountry"
                    className="block text-sm font-medium mb-1"
                  >
                    Country
                  </label>
                  <SelectCountry
                    value={data.bankTransfer?.country || ""}
                    onValueChange={(value) =>
                      handleBankTransferChange("country", value)
                    }
                    id="bankCountry"
                  />
                </div>

                <div>
                  <label
                    htmlFor="bankCity"
                    className="block text-sm font-medium mb-1"
                  >
                    City
                  </label>
                  <Input
                    id="bankCity"
                    value={data.bankTransfer?.city || ""}
                    onChange={(e) =>
                      handleBankTransferChange("city", e.target.value)
                    }
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="bankState"
                    className="block text-sm font-medium mb-1"
                  >
                    State (Optional)
                  </label>
                  <Input
                    id="bankState"
                    value={data.bankTransfer?.state || ""}
                    onChange={(e) =>
                      handleBankTransferChange("state", e.target.value)
                    }
                  />
                </div>

                <div>
                  <label
                    htmlFor="bankZipCode"
                    className="block text-sm font-medium mb-1"
                  >
                    Zip Code
                  </label>
                  <Input
                    id="bankZipCode"
                    value={data.bankTransfer?.zipCode || ""}
                    onChange={(e) =>
                      handleBankTransferChange("zipCode", e.target.value)
                    }
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="bankAddress"
                  className="block text-sm font-medium mb-1"
                >
                  Address
                </label>
                <Input
                  id="bankAddress"
                  value={data.bankTransfer?.address || ""}
                  onChange={(e) =>
                    handleBankTransferChange("address", e.target.value)
                  }
                />
              </div>

              <div>
                <label
                  htmlFor="bankAccountNumber"
                  className="block text-sm font-medium mb-1"
                >
                  IBAN/Account Number
                </label>
                <Input
                  id="bankAccountNumber"
                  value={data.bankTransfer?.accountNumber || ""}
                  onChange={(e) =>
                    handleBankTransferChange("accountNumber", e.target.value)
                  }
                  placeholder="US29 NWBK 6016 1331 9268 19"
                  className={errors.accountNumber ? "border-red-500" : ""}
                />
                {errors.accountNumber && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.accountNumber}
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="swiftCode"
                  className="block text-sm font-medium mb-1"
                >
                  SWIFT Code
                </label>
                <Input
                  id="swiftCode"
                  value={data.bankTransfer?.swiftCode || ""}
                  onChange={(e) =>
                    handleBankTransferChange("swiftCode", e.target.value)
                  }
                  placeholder="NWBKGB22"
                />
              </div>
            </div>
          )}

          {errors.submission && (
            <div className="bg-red-50 text-red-600 p-3 rounded-lg flex items-start">
              <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
              <p className="text-sm">{errors.submission}</p>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-6 bg-secondary/50">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex items-center gap-2"
          disabled={isSubmitting || isLoading}
        >
          <ArrowLeft className="w-4 h-4" /> Back
        </Button>

        <Button
          onClick={onComplete}
          className="flex items-center gap-2 min-w-[140px]"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <div className="h-4 w-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></div>
              Processing...
            </>
          ) : (
            <>Complete Setup</>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default PaymentStep;
