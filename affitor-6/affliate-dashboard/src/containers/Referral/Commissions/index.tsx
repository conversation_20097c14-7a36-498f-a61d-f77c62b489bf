import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectReferralCommissions,
  selectReferralCommissionsLoading,
  selectReferralCommissionsError,
  selectReferralCommissionStats,
  selectReferralCommissionStatsLoading,
} from "@/features/selectors";
import { referralCommissionActions } from "@/features/rootActions";
import Overview from "./Overview";
import History from "./History";

const CommissionsContainer: React.FC = () => {
  const dispatch = useDispatch();

  // Redux selectors
  const commissions = useSelector(selectReferralCommissions);
  const isLoading = useSelector(selectReferralCommissionsLoading);
  const error = useSelector(selectReferralCommissionsError);
  const stats = useSelector(selectReferralCommissionStats);
  const statsLoading = useSelector(selectReferralCommissionStatsLoading);

  // Fetch data on mount
  useEffect(() => {
    // Fetch both stats and commission history
    dispatch(referralCommissionActions.fetchStatsRequest());
    dispatch(
      referralCommissionActions.fetchCommissionsRequest({
        page: 1,
        pageSize: 25,
      })
    );
  }, [dispatch]);

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8 max-w-full overflow-x-hidden">
      <div>
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-800 dark:text-white">
          Commissions
        </h1>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mt-1">
          Track your commission earnings from leads and conversions.
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 sm:p-4">
          <p className="text-sm sm:text-base text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Overview Cards */}
      <Overview loading={statsLoading} stats={stats} />

      {/* Commissions History Table */}
      <History loading={isLoading} />
    </div>
  );
};

export default CommissionsContainer;
