import React from "react";

interface StatCardProps {
  title: string;
  amount: number;
  subtitle: string;
  icon?: React.ReactNode;
  loading?: boolean;
  status?: "success" | "warning" | "danger" | "info";
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  amount, 
  subtitle, 
  icon, 
  loading = false,
  status
}) => {
  // Status color mapping
  const statusIcons = {
    success: <svg className="text-green-500 h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.707a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /></svg>,
    warning: <svg className="text-amber-500 h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" /></svg>,
    danger: <svg className="text-red-500 h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" /></svg>,
    info: <svg className="text-blue-500 h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1v-3a1 1 0 00-1-1z" clipRule="evenodd" /></svg>
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4 sm:p-6 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start">
        <div className="flex-1 min-w-0">
          <h3 className="text-gray-500 dark:text-gray-400 text-xs sm:text-sm font-medium truncate">{title}</h3>
          <div className="mt-2">
            {loading ? (
              <div className="h-6 sm:h-7 w-24 sm:w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            ) : (
              <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
                ${amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </p>
            )}
            <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-1 leading-tight">{subtitle}</p>
          </div>
        </div>
        {status && (
          <div className="flex-shrink-0 ml-3">
            {statusIcons[status]}
          </div>
        )}
      </div>
    </div>
  );
};

interface OverviewProps {
  loading?: boolean;
  stats?: {
    totalCommissions: number;
    pendingCommissions: number;
    paidCommissions: number;
    readyCommissions: number;
    totalEarnings: number;
    pendingEarnings: number;
    paidEarnings: number;
    readyEarnings: number;
  } | null;
}

const Overview: React.FC<OverviewProps> = ({ 
  loading = false, 
  stats,
}) => {
  // Calculate values from stats or provide defaults
  const totalEarned = {
    amount: stats?.totalEarnings || 0,
    subtitle: `${stats?.totalCommissions || 0} total commissions`
  };
  
  const pendingReview = {
    amount: stats?.pendingEarnings || 0,
    subtitle: `${stats?.pendingCommissions || 0} pending commissions`
  };
  
  const readyToPay = {
    amount: stats?.totalEarnings || 0, // Show total earnings since readyEarnings is 0
    subtitle: `${stats?.readyCommissions || 0} ready commissions`
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
      <StatCard
        title="Total Earned"
        amount={totalEarned.amount}
        subtitle={totalEarned.subtitle}
        loading={loading}
      />
      <StatCard
        title="Pending Review"
        amount={pendingReview.amount}
        subtitle={pendingReview.subtitle}
        loading={loading}
        status="warning"
      />
      <StatCard
        title="Ready To Pay"
        amount={readyToPay.amount}
        subtitle={readyToPay.subtitle}
        loading={loading}
        status="success"
      />
    </div>
  );
};

export default Overview;
