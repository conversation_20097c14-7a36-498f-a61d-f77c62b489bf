import React, { useEffect } from "react";
import {
  ArrowUp,
  TrendingDown,
  Minus,
  AlertCircle,
} from "lucide-react";
import { useSelector, useDispatch } from "react-redux";
import { trackLinksActions } from "@/features/rootActions";
import { RootState } from "@/store";

interface QuickOverviewCardProps {
  title: string;
  value: number;
  description: string;
  trend?: "up" | "down" | "stable";
  trendColor?: string;
  isLoading?: boolean;
}

const QuickOverviewCard: React.FC<QuickOverviewCardProps> = ({
  title,
  value,
  description,
  trend = "stable",
  trendColor,
  isLoading = false,
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case "up":
        return <ArrowUp className="w-4 h-4" />;
      case "down":
        return <TrendingDown className="w-4 h-4" />;
      default:
        return <Minus className="w-4 h-4" />;
    }
  };

  const defaultTrendColor = () => {
    switch (trend) {
      case "up":
        return "text-green-500";
      case "down":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
        <div className="animate-pulse">
          <div className="flex justify-between items-start">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
            <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
          <div className="mt-2">
            <div className="h-6 sm:h-8 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
          </div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32 mt-1"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start">
        <h3 className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400 truncate pr-2">
          {title}
        </h3>
        <div className={`flex-shrink-0 ${trendColor || defaultTrendColor()}`}>
          {getTrendIcon()}
        </div>
      </div>
      <div className="mt-2">
        <span className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
          {value.toLocaleString()}
        </span>
      </div>
      <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-1 leading-tight">
        {description}
      </p>
    </div>
  );
};

const QuickOverview: React.FC = () => {
  const dispatch = useDispatch();
  const overview = useSelector((state: RootState) => state.trackLinks.overview);
  const isLoading = useSelector((state: RootState) => state.trackLinks.loading);
  const error = useSelector((state: RootState) => state.trackLinks.error);

  useEffect(() => {
    dispatch(trackLinksActions.fetchOverviewRequest());
  }, [dispatch]);

  // if (error) {
  //   return (
  //     <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
  //       <div className="flex items-center">
  //         <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
  //         <p className="text-red-700 dark:text-red-400">
  //           Failed to load overview: {error}
  //         </p>
  //       </div>
  //     </div>
  //   );
  // }

  const mapTrend = (
    trend: "increase" | "decrease" | "stable"
  ): "up" | "down" | "stable" => {
    switch (trend) {
      case "increase":
        return "up";
      case "decrease":
        return "down";
      default:
        return "stable";
    }
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
      <QuickOverviewCard
        title="Total Visitors"
        value={overview?.visitors.total || 0}
        description="All visitors from your links"
        trend={overview ? mapTrend(overview.visitors.trend) : "stable"}
        isLoading={isLoading}
      />
      <QuickOverviewCard
        title="Total Leads"
        value={overview?.leads.total || 0}
        description="All leads from your links"
        trend={overview ? mapTrend(overview.leads.trend) : "stable"}
        isLoading={isLoading}
      />
      <QuickOverviewCard
        title="Total Conversions"
        value={overview?.conversions.total || 0}
        description="All conversions from your links"
        trend={overview ? mapTrend(overview.conversions.trend) : "stable"}
        trendColor="text-green-500"
        isLoading={isLoading}
      />
    </div>
  );
};

export default QuickOverview;
