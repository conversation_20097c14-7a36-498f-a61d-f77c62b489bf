import React, { useState, useEffect } from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { useSelector, useDispatch } from "react-redux";
import { trackLinksActions } from "@/features/rootActions";
import { RootState } from "@/store";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

type TimePeriod = "weekly" | "monthly" | "yearly";

const PerformanceOverview: React.FC = () => {
  const [timePeriod, setTimePeriod] = useState<TimePeriod>("weekly");
  const dispatch = useDispatch();

  const performanceOverview = useSelector(
    (state: RootState) => state.trackLinks.performanceOverview
  );
  const performanceLoading = useSelector(
    (state: RootState) => state.trackLinks.performanceLoading
  );

  useEffect(() => {
    // Map time period to API format
    const periodMap = {
      weekly: "weekly",
      monthly: "monthly",
      yearly: "yearly",
    };

    dispatch(
      trackLinksActions.fetchPerformanceOverviewRequest({
        period: periodMap[timePeriod],
      })
    );
  }, [dispatch, timePeriod]);

  // Generate chart data from performance overview or use fallback
  const chartData = {
    labels: performanceOverview?.time || [
      "Apr 30",
      "May 07",
      "May 14",
      "May 21",
    ],
    datasets: [
      {
        label: "Visitors",
        data: performanceOverview?.visitors || [0, 0, 0, 0],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.5)",
        tension: 0,
      },
      {
        label: "Leads",
        data: performanceOverview?.leads || [0, 0, 0, 0],
        borderColor: "rgb(245, 158, 11)",
        backgroundColor: "rgba(245, 158, 11, 0.5)",
        tension: 0,
      },
      {
        label: "Conversions",
        data: performanceOverview?.conversions || [0, 0, 0, 0],
        borderColor: "rgb(16, 185, 129)",
        backgroundColor: "rgba(16, 185, 129, 0.5)",
        tension: 0,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: true,
        },
        border: {
          display: false,
        },
      },
      x: {
        grid: {
          display: false,
        },
        border: {
          display: false,
        },
      },
    },
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-3">
        <h2 className="text-lg sm:text-xl font-medium text-gray-900 dark:text-white">
          Performance Overview
        </h2>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setTimePeriod("weekly")}
            className={`px-3 py-2 text-sm rounded-md transition-colors min-h-[44px] sm:min-h-[36px] ${
              timePeriod === "weekly"
                ? "bg-blue-600 text-white"
                : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            }`}
            disabled={performanceLoading}
          >
            Weekly
          </button>
          <button
            onClick={() => setTimePeriod("monthly")}
            className={`px-3 py-2 text-sm rounded-md transition-colors min-h-[44px] sm:min-h-[36px] ${
              timePeriod === "monthly"
                ? "bg-blue-600 text-white"
                : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            }`}
            disabled={performanceLoading}
          >
            Monthly
          </button>
          <button
            onClick={() => setTimePeriod("yearly")}
            className={`px-3 py-2 text-sm rounded-md transition-colors min-h-[44px] sm:min-h-[36px] ${
              timePeriod === "yearly"
                ? "bg-blue-600 text-white"
                : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            }`}
            disabled={performanceLoading}
          >
            Yearly
          </button>
        </div>
      </div>
      <div className="h-[250px] sm:h-[300px] lg:h-[350px] relative">
        {performanceLoading && (
          <div className="absolute inset-0 bg-gray-50 dark:bg-gray-700 bg-opacity-50 flex items-center justify-center z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        )}
        <Line options={chartOptions} data={chartData} />
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-4 gap-3">
        <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
          Performance data from your links
        </p>

        {/* Color Legend */}
        <div className="flex flex-wrap items-center gap-4 text-xs sm:text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-gray-500 dark:text-gray-400">Visitors</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-amber-500"></div>
            <span className="text-gray-500 dark:text-gray-400">Leads</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-emerald-500"></div>
            <span className="text-gray-500 dark:text-gray-400">
              Conversions
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceOverview;
