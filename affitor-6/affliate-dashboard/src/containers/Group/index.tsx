import React from 'react';
import Link from 'next/link';
import styles from './Group.module.css';

export const Group: React.FC = () => {
  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-80px)] px-4">
      <div className="max-w-2xl w-full bg-white rounded-lg shadow-[0_0_15px_rgba(0,0,0,0.1)] hover:shadow-[0_0_20px_rgba(0,0,0,0.15)] transition-shadow duration-300 p-8">
        <h1 className="text-center text-3xl font-bold text-blue-500 mb-6">
          Join Our Community
        </h1>
        
        <p className="text-center text-gray-700 mb-8">
          Connect with other members, share experiences, and get quick
          support through Affitor's official Telegram group.
        </p>
        
        <div className="flex justify-center">
          <Link 
            href="https://t.me/affitor_cs"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-8 rounded-md transition-colors"
          >
            Join Now
          </Link>
        </div>
      </div>
    </div>
  );
};
