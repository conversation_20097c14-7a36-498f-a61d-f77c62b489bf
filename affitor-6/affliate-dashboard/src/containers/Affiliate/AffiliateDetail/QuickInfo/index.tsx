import { IPaymentMethod, ICategory } from "@/interfaces";
import PaymentMethod from "@/components/PaymentMethod";
import { useRouter } from "next/router";

interface QuickInfoProps {
  paymentMethods: IPaymentMethod[];
  minimumPayout: number;
  currency: string;
  launchYear: number;
  cookieDuration: string;
  contactInfo?: string;
  avg_conversion: number;
  categories: ICategory[];
}

export default function QuickInfo({
  paymentMethods,
  minimumPayout,
  currency,
  launchYear,
  cookieDuration,
  contactInfo,
  avg_conversion,
  categories,
}: QuickInfoProps) {
  const router = useRouter();
  return (
    <div className="p-4 bg-primary shadow rounded-lg text-[12px] md:text-[14px] h-full">
      <h2 className="text-lg md:text-xl font-bold mb-4 text-primary-foreground">
        Quick Info
      </h2>
      <ul className="space-y-2">
        <BasicInfo title="Payment Method">
          <div className="flex gap-2 flex-wrap">
            {paymentMethods &&
              paymentMethods.map((method, i) => (
                <PaymentMethod method={method} key={`payment_${i}`} />
              ))}
          </div>
        </BasicInfo>
        <BasicInfo title="Minimum Payout">
          <span className="font-semibold flex items-end text-xs md:text-base">
            {minimumPayout ? (
              <>
                {currency === "EUR" ? "€" : "$"}
                <span>{minimumPayout.toFixed(2)}</span>
              </>
            ) : (
              "_"
            )}
          </span>
        </BasicInfo>
        <BasicInfo title="Launch Year">
          <span className="font-semibold text-xs md:text-base text-primary-foreground">
            {launchYear || "_"}
          </span>
        </BasicInfo>
        <BasicInfo title="Cookie Duration">
          <span className="font-semibold text-xs md:text-base text-primary-foreground">
            {cookieDuration ? cookieDuration + " days" : "_"}
          </span>
        </BasicInfo>
        <BasicInfo title="Avg $/Conversion">
          <span className="font-semibold flex items-end text-xs md:text-base">
            {avg_conversion ? (
              <>
                {currency === "EUR" ? "€" : "$"}
                <span>{avg_conversion.toFixed(2)}</span>
              </>
            ) : (
              "_"
            )}
          </span>
        </BasicInfo>
        <BasicInfo title="Category">
          {categories.length == 0 ? (
            "_"
          ) : (
            <div className="gap-1 flex text-primary-foreground">
              {categories.map((category, index) => {
                return (
                  <span
                    key={`category-${index}`}
                    className="font-semibold px-2 py-[2px] bg-blue-200 rounded-lg text-[10px] md:text-xs cursor-pointer hover:bg-blue-300 transition-colors"
                    onClick={() => {
                      if (category.slug) {
                        router.push(`/${category.slug}`);
                      }
                    }}
                  >
                    {category.name}
                  </span>
                );
              })}
            </div>
          )}
        </BasicInfo>
      </ul>
    </div>
  );
}

function BasicInfo({
  children,
  title,
}: {
  children: React.ReactNode;
  title: string;
}) {
  return (
    <div className="flex justify-between border-b border-gray-200 pb-2 items-center">
      <span className="text-xs md:text-sm text-primary-foreground/80">
        {title}
      </span>
      {children}
    </div>
  );
}
