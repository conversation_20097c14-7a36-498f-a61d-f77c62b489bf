import type { NextApiRequest, NextApiResponse } from "next";
import { createApiContext } from "@/utils/api-middleware";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const sessionId = req.query["session-id"] as string;

    if (!sessionId) {
      return res.status(400).json({ message: "Session ID is required" });
    }

    // Use centralized context
    const { token, cookies } = createApiContext(req, { forwardCookies: true });

    // Use StrapiClient method for consistency
    const response = await StrapiClient.confirmCheckoutSession(
      sessionId,
      token,
      cookies
    );

    return res.status(200).json(response);
  } catch (error: any) {
    console.error("Error confirming checkout session:", error);
    sendApiError(res, error, "An error occurred while confirming checkout");
  }
}
