import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient, StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const token = req.headers.authorization?.split(" ")[1]; // Extract the token from the Authorization header

    if (!token) {
      return res.status(401).json({ error: "Authentication required" });
    }

    // Call the appropriate StrapiClient method based on HTTP method
    if (req.method === "GET") {
      // Serialize the query object into a query string like in affiliate API
      const queryString = req.url?.split("?")[1] || "";

      const response = await StrapiClient.getReferrerLinks(queryString, token);
      return res.status(200).json(response);
    } else if (req.method === "POST") {
      const { name, url, shortLink, partnerId, partnerUserId, selectedPage } = req.body;
      if (!name || !url) {
        return res.status(400).json({ error: "Name and URL are required" });
      }

      // If partnerId is provided (admin creating for specific partner)
      if (partnerId && partnerUserId) {
        // Extract IDs from the request body
        const { userId, userDocumentId, referrerId, referrerDocumentId } =
          req.body;

        const response = await StrapiAdminClient.createReferrerLinkForPartner(
          {
            name,
            url,
            shortLink,
            userId: userId || partnerUserId,
            userDocumentId: userDocumentId || req.body.partnerUserDocumentId,
            referrerId: referrerId || partnerId,
            referrerDocumentId:
              referrerDocumentId || req.body.partnerDocumentId,
          },
          token
        );
        return res.status(201).json(response);
      } else {
        // Regular user creating their own link - call Strapi directly
        const dataToSend: any = {
          name,
          url,
          short_link: shortLink,
        };

        // Include page_id if selectedPage is provided
        if (selectedPage && selectedPage.trim() !== "") {
          dataToSend.page_id = selectedPage;
        }

        const response = await StrapiClient.client.post(
          "/api/referrer-links",
          {
            data: dataToSend,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        return res.status(201).json(response.data);
      }
    } else {
      return res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error: any) {
    console.error("Error handling referrer links request:", error);
    sendApiError(res, error, error.message);
  }
}
