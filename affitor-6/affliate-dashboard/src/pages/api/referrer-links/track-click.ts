import type { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { url, shortLink } = req.body;

    if (!url && !shortLink) {
      return res.status(400).json({ error: "Either URL or short link is required" });
    }

    const { token } = createApiContext(req, { requireAuth: false });

    // Forward the request to Strapi backend with enhanced parameters
    const response = await StrapiClient.trackReferralClick(url, token, shortLink);

    return res.status(200).json(response);
  } catch (error: any) {
    console.error("Error tracking referral click:", error);
    return res.status(500).json({
      error: "Failed to track referral click",
      message: error.message,
    });
  }
}
