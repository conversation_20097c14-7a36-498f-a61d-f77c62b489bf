import { AppError } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";
import { sendApiError } from "@/utils/api-error-handler";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ play: string } | AppError>
) {
  try {
    const { videoId } = req.query;
    
    if (!videoId || typeof videoId !== 'string') {
      return sendApiError(res, {
        statusCode: 400,
        message: "Invalid video ID",
      }, "Invalid video ID");
    }
    
    const response = await StrapiClient.getTikTokVideoUrl(videoId);
    res.status(200).json(response);
  } catch (error: any) {
    console.error("TikTok video URL API error:", error);
    sendApiError(res, error, "Error fetching TikTok video URL");
  }
}
