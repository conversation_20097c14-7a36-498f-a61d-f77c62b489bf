// Simple test script to check partners API
const fetch = require('node-fetch');

async function testPartnersAPI() {
  try {
    console.log('Testing partners API...');
    
    // Test without authentication first
    const response = await fetch('http://localhost:3001/api/admin/partners?pageSize=10', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());
    
    const data = await response.text();
    console.log('Response body:', data);
    
  } catch (error) {
    console.error('Error testing partners API:', error);
  }
}

testPartnersAPI();
